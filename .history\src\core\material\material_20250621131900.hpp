// src/core/material/material.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Material system

#pragma once

#include "../math/vec3.hpp"
#include "../math/color3.hpp"
#include "../scene/scene.hpp"
#include "disney_brdf.hpp"
#include <memory>
#include <string>

namespace photon {

// Forward declarations
class Sampler;
struct Intersection;
class Texture;
struct Vec2;

/**
 * @brief BSDF sample structure
 */
struct BSDFSample {
    Color3 f;           ///< BSDF value
    Vec3 wi;            ///< Incident direction (towards surface)
    float pdf;          ///< Probability density function
    bool isDelta;       ///< Is delta distribution (perfect reflection/transmission)
    
    BSDFSample() : f(0), wi(0), pdf(0), isDelta(false) {}
    BSDFSample(const Color3& f, const Vec3& wi, float pdf, bool isDelta = false)
        : f(f), wi(wi), pdf(pdf), isDel<PERSON>(isDelta) {}
    
    bool isValid() const { return pdf > 0.0f && !f.isZero(); }
};

/**
 * @brief Abstract base material class
 */
class Material {
public:
    Material() = default;
    virtual ~Material() = default;
    
    // Non-copyable
    Material(const Material&) = delete;
    Material& operator=(const Material&) = delete;
    
    /**
     * @brief Evaluate BSDF
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction (from surface)
     * @param wi Incident direction (towards surface)
     * @return BSDF value
     */
    virtual Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const = 0;
    
    /**
     * @brief Sample BSDF direction
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction (from surface)
     * @param sampler Random sampler
     * @return BSDF sample
     */
    virtual BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const = 0;
    
    /**
     * @brief Get BSDF PDF for given directions
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction (from surface)
     * @param wi Incident direction (towards surface)
     * @return Probability density
     */
    virtual float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const = 0;
    
    /**
     * @brief Get emitted radiance (for emissive materials)
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction
     * @return Emitted radiance
     */
    virtual Color3 Le(const Intersection& isect, const Vec3& wo) const { return Color3(0); }
    
    /**
     * @brief Check if material is emissive
     */
    virtual bool isEmissive() const { return false; }
    
    /**
     * @brief Get material name
     */
    virtual std::string getName() const = 0;
};

/**
 * @brief Lambertian diffuse material
 */
class DiffuseMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param albedo Diffuse reflectance
     */
    DiffuseMaterial(const Color3& albedo = Color3(0.8f));
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    std::string getName() const override { return "Diffuse"; }
    
    /**
     * @brief Set albedo
     */
    void setAlbedo(const Color3& albedo) { m_albedo = albedo; }
    
    /**
     * @brief Get albedo
     */
    const Color3& getAlbedo() const { return m_albedo; }

private:
    Color3 m_albedo;
};

/**
 * @brief Perfect mirror material
 */
class MirrorMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param reflectance Mirror reflectance
     */
    MirrorMaterial(const Color3& reflectance = Color3(0.9f));
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    std::string getName() const override { return "Mirror"; }

private:
    Color3 m_reflectance;
};

/**
 * @brief Emissive material (area light)
 */
class EmissiveMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param emission Emitted radiance
     */
    EmissiveMaterial(const Color3& emission = Color3(1.0f));
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    Color3 Le(const Intersection& isect, const Vec3& wo) const override;
    bool isEmissive() const override { return true; }
    std::string getName() const override { return "Emissive"; }
    
    /**
     * @brief Set emission
     */
    void setEmission(const Color3& emission) { m_emission = emission; }
    
    /**
     * @brief Get emission
     */
    const Color3& getEmission() const { return m_emission; }

private:
    Color3 m_emission;
};

/**
 * @brief Plastic material (diffuse + specular)
 */
class PlasticMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param diffuse Diffuse component
     * @param specular Specular component
     * @param roughness Surface roughness [0,1]
     */
    PlasticMaterial(const Color3& diffuse = Color3(0.5f), 
                   const Color3& specular = Color3(0.1f),
                   float roughness = 0.1f);
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    std::string getName() const override { return "Plastic"; }

private:
    Color3 m_diffuse;
    Color3 m_specular;
    float m_roughness;
    
    /**
     * @brief Fresnel reflectance
     */
    float fresnel(float cosTheta) const;
    
    /**
     * @brief Microfacet distribution
     */
    float distribution(const Vec3& wh, float alpha) const;
    
    /**
     * @brief Masking-shadowing function
     */
    float geometry(const Vec3& wo, const Vec3& wi, const Vec3& wh, float alpha) const;
};

/**
 * @brief Disney Principled BRDF Material
 *
 * Implements the Disney Principled BRDF for photorealistic rendering
 */
class PBRMaterial : public Material {
public:
    /**
     * @brief Constructor with default parameters
     */
    PBRMaterial();

    /**
     * @brief Constructor with Disney BRDF parameters
     * @param params Disney BRDF parameters
     */
    explicit PBRMaterial(const DisneyBRDFParams& params);

    /**
     * @brief Constructor with basic parameters
     * @param baseColor Base color (albedo)
     * @param metallic Metallic parameter [0,1]
     * @param roughness Roughness parameter [0,1]
     */
    PBRMaterial(const Color3& baseColor, float metallic = 0.0f, float roughness = 0.5f);

    // Material interface implementation
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    std::string getName() const override { return "PBR"; }

    /**
     * @brief Set Disney BRDF parameters
     * @param params New parameters
     */
    void setParameters(const DisneyBRDFParams& params);

    /**
     * @brief Get current parameters
     * @return Current Disney BRDF parameters
     */
    const DisneyBRDFParams& getParameters() const;

    /**
     * @brief Set base color
     * @param color New base color
     */
    void setBaseColor(const Color3& color);

    /**
     * @brief Set metallic parameter
     * @param metallic Metallic value [0,1]
     */
    void setMetallic(float metallic);

    /**
     * @brief Set roughness parameter
     * @param roughness Roughness value [0,1]
     */
    void setRoughness(float roughness);

    /**
     * @brief Set specular parameter
     * @param specular Specular value [0,1]
     */
    void setSpecular(float specular);

    /**
     * @brief Get base color
     * @return Current base color
     */
    const Color3& getBaseColor() const;

    /**
     * @brief Get metallic parameter
     * @return Current metallic value
     */
    float getMetallic() const;

    /**
     * @brief Get roughness parameter
     * @return Current roughness value
     */
    float getRoughness() const;

    /**
     * @brief Create material from preset
     * @param preset Preset type
     * @param color Base color
     * @return PBR material with preset parameters
     */
    static std::shared_ptr<PBRMaterial> createPreset(const std::string& preset, const Color3& color = Color3(0.8f));

    /**
     * @brief Validate energy conservation
     * @return True if material respects energy conservation
     */
    bool validateEnergyConservation() const;

    /**
     * @brief Set texture for base color
     * @param texture Base color texture
     */
    void setBaseColorTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set texture for metallic parameter
     * @param texture Metallic texture (single channel)
     */
    void setMetallicTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set texture for roughness parameter
     * @param texture Roughness texture (single channel)
     */
    void setRoughnessTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set texture for normal mapping
     * @param texture Normal map texture
     */
    void setNormalTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set texture for specular parameter
     * @param texture Specular texture (single channel)
     */
    void setSpecularTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set bump texture (height map)
     * @param texture Bump texture (grayscale height map)
     */
    void setBumpTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set bump intensity
     * @param intensity Bump mapping intensity [0,1]
     */
    void setBumpIntensity(float intensity);

    /**
     * @brief Set normal intensity
     * @param intensity Normal mapping intensity [0,1]
     */
    void setNormalIntensity(float intensity);

    /**
     * @brief Set secondary normal texture for blending
     * @param texture Secondary normal texture
     */
    void setSecondaryNormalTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set normal blend factor
     * @param factor Blend factor between primary and secondary normal [0,1]
     */
    void setNormalBlendFactor(float factor);

    /**
     * @brief Set parallax scale
     * @param scale Parallax mapping scale factor [0,1]
     */
    void setParallaxScale(float scale);

    /**
     * @brief Set parallax layers for steep parallax mapping
     * @param layers Number of layers for steep parallax [8,64]
     */
    void setParallaxLayers(int layers);

    /**
     * @brief Enable/disable relief mapping
     * @param enabled Relief mapping enabled
     */
    void setReliefMappingEnabled(bool enabled);

    /**
     * @brief Get base color texture
     */
    std::shared_ptr<Texture> getBaseColorTexture() const { return m_baseColorTexture; }

    /**
     * @brief Get metallic texture
     */
    std::shared_ptr<Texture> getMetallicTexture() const { return m_metallicTexture; }

    /**
     * @brief Get roughness texture
     */
    std::shared_ptr<Texture> getRoughnessTexture() const { return m_roughnessTexture; }

    /**
     * @brief Get normal texture
     */
    std::shared_ptr<Texture> getNormalTexture() const { return m_normalTexture; }

    /**
     * @brief Get bump texture
     */
    std::shared_ptr<Texture> getBumpTexture() const { return m_bumpTexture; }

    /**
     * @brief Get bump intensity
     */
    float getBumpIntensity() const { return m_bumpIntensity; }

    /**
     * @brief Get normal intensity
     */
    float getNormalIntensity() const { return m_normalIntensity; }

    /**
     * @brief Get secondary normal texture
     */
    std::shared_ptr<Texture> getSecondaryNormalTexture() const { return m_secondaryNormalTexture; }

    /**
     * @brief Get normal blend factor
     */
    float getNormalBlendFactor() const { return m_normalBlendFactor; }

    /**
     * @brief Get parallax scale
     */
    float getParallaxScale() const { return m_parallaxScale; }

    /**
     * @brief Get parallax layers
     */
    int getParallaxLayers() const { return m_parallaxLayers; }

    /**
     * @brief Get relief mapping enabled state
     */
    bool isReliefMappingEnabled() const { return m_reliefMappingEnabled; }

    /**
     * @brief Evaluate material with texture support
     * @param isect Intersection data with UV coordinates
     * @param wo Outgoing direction
     * @param wi Incident direction
     * @return BRDF value with textures applied
     */
    Color3 evaluateWithTextures(const Intersection& isect, const Vec3& wo, const Vec3& wi) const;

    /**
     * @brief Sample material with texture support
     * @param isect Intersection data with UV coordinates
     * @param wo Outgoing direction
     * @param sampler Random sampler
     * @return BSDF sample with textures applied
     */
    BSDFSample sampleWithTextures(const Intersection& isect, const Vec3& wo, Sampler& sampler) const;

    /**
     * @brief Apply normal mapping to surface normal
     * @param uv UV coordinates
     * @param normal Original surface normal
     * @param tangent Surface tangent
     * @param bitangent Surface bitangent
     * @return Modified normal
     */
    Vec3 applyNormalMapping(const Vec2& uv, const Vec3& normal, const Vec3& tangent, const Vec3& bitangent) const;

    /**
     * @brief Apply bump mapping to surface normal
     * @param uv Texture coordinates
     * @param normal Original surface normal
     * @param tangent Surface tangent
     * @param bitangent Surface bitangent
     * @return Modified normal
     */
    Vec3 applyBumpMapping(const Vec2& uv, const Vec3& normal, const Vec3& tangent, const Vec3& bitangent) const;

    /**
     * @brief Combine normal and bump mapping effects
     * @param uv Texture coordinates
     * @param normal Original surface normal
     * @param tangent Surface tangent
     * @param bitangent Surface bitangent
     * @return Final modified normal
     */
    Vec3 applySurfaceMapping(const Vec2& uv, const Vec3& normal, const Vec3& tangent, const Vec3& bitangent) const;

    /**
     * @brief Apply advanced normal mapping with intensity and blending
     * @param uv Texture coordinates
     * @param normal Original surface normal
     * @param tangent Surface tangent
     * @param bitangent Surface bitangent
     * @return Modified normal with advanced features
     */
    Vec3 applyAdvancedNormalMapping(const Vec2& uv, const Vec3& normal, const Vec3& tangent, const Vec3& bitangent) const;

    /**
     * @brief Apply parallax mapping to UV coordinates
     * @param uv Original texture coordinates
     * @param viewDir View direction in tangent space
     * @return Modified UV coordinates with parallax offset
     */
    Vec2 applyParallaxMapping(const Vec2& uv, const Vec3& viewDir) const;

    /**
     * @brief Apply steep parallax mapping with multiple samples
     * @param uv Original texture coordinates
     * @param viewDir View direction in tangent space
     * @return Modified UV coordinates with steep parallax offset
     */
    Vec2 applySteepParallaxMapping(const Vec2& uv, const Vec3& viewDir) const;

    /**
     * @brief Apply relief mapping with ray tracing
     * @param uv Original texture coordinates
     * @param viewDir View direction in tangent space
     * @return Modified UV coordinates with relief mapping offset
     */
    Vec2 applyReliefMapping(const Vec2& uv, const Vec3& viewDir) const;

private:
    DisneyBRDF m_brdf;

    // Texture maps for PBR workflow
    std::shared_ptr<Texture> m_baseColorTexture;
    std::shared_ptr<Texture> m_metallicTexture;
    std::shared_ptr<Texture> m_roughnessTexture;
    std::shared_ptr<Texture> m_normalTexture;
    std::shared_ptr<Texture> m_specularTexture;
    std::shared_ptr<Texture> m_bumpTexture;
    std::shared_ptr<Texture> m_secondaryNormalTexture;

    // Surface mapping parameters
    float m_bumpIntensity = 1.0f;
    float m_normalIntensity = 1.0f;
    float m_normalBlendFactor = 0.0f;

    // Parallax mapping parameters
    float m_parallaxScale = 0.1f;
    int m_parallaxLayers = 16;
    bool m_reliefMappingEnabled = false;

    /**
     * @brief Apply textures to Disney BRDF parameters
     * @param uv UV coordinates
     * @return Modified parameters with texture values
     */
    DisneyBRDFParams applyTextures(const Vec2& uv) const;

private:
};

} // namespace photon

// Include Disney BRDF implementation
#include "disney_brdf.hpp"
