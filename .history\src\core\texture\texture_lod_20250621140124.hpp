// src/core/texture/texture_lod.hpp
// PhotonRender - Level of Detail (LOD) System
// Automatic LOD generation and management for texture optimization

#pragma once

#include "texture.hpp"
#include "../math/vec2.hpp"
#include "../math/vec3.hpp"
#include "../math/color3.hpp"
#include <memory>
#include <vector>
#include <cmath>

namespace photon {

/**
 * @brief Mipmap filtering algorithms
 */
enum class MipmapFilter {
    BOX,            ///< Simple box filter (fastest)
    BILINEAR,       ///< Bilinear filtering (balanced)
    BICUBIC,        ///< Bicubic filtering (highest quality)
    LANCZOS         ///< Lanczos filtering (sharp, high quality)
};

/**
 * @brief LOD selection method
 */
enum class LODSelectionMethod {
    DISTANCE,       ///< Distance-based selection
    SCREEN_SIZE,    ///< Screen space size-based
    MANUAL,         ///< Manual LOD specification
    ADAPTIVE        ///< Adaptive based on performance
};

/**
 * @brief LOD generation settings
 */
struct LODSettings {
    MipmapFilter filter = MipmapFilter::BILINEAR;
    int maxLevels = 12;                 ///< Maximum mipmap levels
    float lodBias = 0.0f;               ///< LOD bias (-2.0 to 2.0)
    bool generateMipmaps = true;        ///< Generate mipmap chain
    bool preserveAlpha = true;          ///< Preserve alpha channel quality
    float sharpness = 1.0f;             ///< Sharpness factor [0,2]
    
    static LODSettings defaultSettings() {
        return LODSettings{MipmapFilter::BILINEAR, 12, 0.0f, true, true, 1.0f};
    }
    
    static LODSettings highQuality() {
        return LODSettings{MipmapFilter::BICUBIC, 16, -0.5f, true, true, 1.2f};
    }
    
    static LODSettings performance() {
        return LODSettings{MipmapFilter::BOX, 8, 0.5f, true, false, 0.8f};
    }
};

/**
 * @brief LOD level information
 */
struct LODLevel {
    int level;                          ///< Mipmap level (0 = full resolution)
    int width;                          ///< Width at this level
    int height;                         ///< Height at this level
    std::vector<float> data;            ///< Texture data for this level
    size_t memoryUsage;                 ///< Memory usage in bytes
    
    LODLevel(int lvl, int w, int h) 
        : level(lvl), width(w), height(h), memoryUsage(0) {}
};

/**
 * @brief LOD-enabled texture class
 */
class LODTexture : public ImageTexture {
public:
    /**
     * @brief Constructor
     */
    LODTexture();
    
    /**
     * @brief Constructor with data
     * @param width Texture width
     * @param height Texture height
     * @param channels Number of channels
     * @param data Texture data
     */
    LODTexture(int width, int height, int channels, const float* data);
    
    /**
     * @brief Destructor
     */
    virtual ~LODTexture() = default;
    
    /**
     * @brief Generate mipmap chain
     * @param settings LOD generation settings
     * @return True if successful
     */
    bool generateMipmaps(const LODSettings& settings = LODSettings::defaultSettings());
    
    /**
     * @brief Sample texture with automatic LOD selection
     * @param uv UV coordinates
     * @param distance Distance from camera
     * @param screenSize Screen space size
     * @return Sampled color
     */
    Color3 sampleLOD(const Vec2& uv, float distance, float screenSize = 1.0f) const;
    
    /**
     * @brief Sample texture at specific LOD level
     * @param uv UV coordinates
     * @param lodLevel LOD level (can be fractional)
     * @return Sampled color
     */
    Color3 sampleAtLOD(const Vec2& uv, float lodLevel) const;
    
    /**
     * @brief Calculate LOD level based on distance
     * @param distance Distance from camera
     * @param screenSize Screen space size
     * @return LOD level
     */
    float calculateLOD(float distance, float screenSize = 1.0f) const;
    
    /**
     * @brief Get number of mipmap levels
     */
    int getMipmapLevels() const { return static_cast<int>(m_lodLevels.size()); }
    
    /**
     * @brief Get LOD level information
     * @param level LOD level
     * @return LOD level info or nullptr if invalid
     */
    const LODLevel* getLODLevel(int level) const;
    
    /**
     * @brief Get total memory usage for all LOD levels
     */
    size_t getTotalMemoryUsage() const;
    
    /**
     * @brief Set LOD bias
     * @param bias LOD bias (-2.0 to 2.0)
     */
    void setLODBias(float bias) { m_lodBias = std::clamp(bias, -2.0f, 2.0f); }
    
    /**
     * @brief Get LOD bias
     */
    float getLODBias() const { return m_lodBias; }
    
    /**
     * @brief Set LOD selection method
     */
    void setLODSelectionMethod(LODSelectionMethod method) { m_selectionMethod = method; }
    
    /**
     * @brief Get LOD selection method
     */
    LODSelectionMethod getLODSelectionMethod() const { return m_selectionMethod; }
    
    /**
     * @brief Enable/disable trilinear filtering
     */
    void setTrilinearFiltering(bool enabled) { m_trilinearFiltering = enabled; }
    
    /**
     * @brief Check if trilinear filtering is enabled
     */
    bool isTrilinearFilteringEnabled() const { return m_trilinearFiltering; }

private:
    std::vector<LODLevel> m_lodLevels;      ///< Mipmap levels
    float m_lodBias = 0.0f;                 ///< LOD bias
    LODSelectionMethod m_selectionMethod = LODSelectionMethod::DISTANCE;
    bool m_trilinearFiltering = true;       ///< Enable trilinear filtering
    LODSettings m_lodSettings;              ///< LOD generation settings
    
    /**
     * @brief Generate single mipmap level
     * @param sourceLevel Source LOD level
     * @param filter Filtering method
     * @return Generated LOD level
     */
    LODLevel generateMipmapLevel(const LODLevel& sourceLevel, MipmapFilter filter) const;
    
    /**
     * @brief Apply box filter
     * @param source Source data
     * @param srcWidth Source width
     * @param srcHeight Source height
     * @param channels Number of channels
     * @return Filtered data
     */
    std::vector<float> applyBoxFilter(const std::vector<float>& source, 
                                     int srcWidth, int srcHeight, int channels) const;
    
    /**
     * @brief Apply bilinear filter
     */
    std::vector<float> applyBilinearFilter(const std::vector<float>& source, 
                                          int srcWidth, int srcHeight, int channels) const;
    
    /**
     * @brief Apply bicubic filter
     */
    std::vector<float> applyBicubicFilter(const std::vector<float>& source, 
                                         int srcWidth, int srcHeight, int channels) const;
    
    /**
     * @brief Sample from specific LOD level
     * @param level LOD level
     * @param uv UV coordinates
     * @return Sampled color
     */
    Color3 sampleFromLevel(const LODLevel& level, const Vec2& uv) const;
    
    /**
     * @brief Interpolate between two LOD levels (trilinear filtering)
     * @param level0 First LOD level
     * @param level1 Second LOD level
     * @param uv UV coordinates
     * @param t Interpolation factor [0,1]
     * @return Interpolated color
     */
    Color3 interpolateLevels(const LODLevel& level0, const LODLevel& level1, 
                            const Vec2& uv, float t) const;
    
    /**
     * @brief Calculate maximum number of mipmap levels
     * @param width Texture width
     * @param height Texture height
     * @return Maximum levels
     */
    int calculateMaxLevels(int width, int height) const;
    
    /**
     * @brief Cubic interpolation function for bicubic filtering
     */
    float cubicInterpolate(float p0, float p1, float p2, float p3, float t) const;

    /**
     * @brief Clamp UV coordinates for sampling
     */
    Vec2 clampUV(const Vec2& uv) const;
};

/**
 * @brief LOD Manager for global LOD control
 */
class LODManager {
public:
    /**
     * @brief Get singleton instance
     */
    static LODManager& getInstance();
    
    /**
     * @brief Set global LOD bias
     * @param bias Global LOD bias
     */
    void setGlobalLODBias(float bias) { m_globalLODBias = bias; }
    
    /**
     * @brief Get global LOD bias
     */
    float getGlobalLODBias() const { return m_globalLODBias; }
    
    /**
     * @brief Set LOD distance scale
     * @param scale Distance scale factor
     */
    void setLODDistanceScale(float scale) { m_lodDistanceScale = scale; }
    
    /**
     * @brief Get LOD distance scale
     */
    float getLODDistanceScale() const { return m_lodDistanceScale; }
    
    /**
     * @brief Calculate screen space size
     * @param worldSize Object size in world space
     * @param distance Distance from camera
     * @param fov Field of view in radians
     * @param screenHeight Screen height in pixels
     * @return Screen space size
     */
    float calculateScreenSize(float worldSize, float distance, float fov, int screenHeight) const;

private:
    float m_globalLODBias = 0.0f;
    float m_lodDistanceScale = 1.0f;
    
    LODManager() = default;
};

} // namespace photon
