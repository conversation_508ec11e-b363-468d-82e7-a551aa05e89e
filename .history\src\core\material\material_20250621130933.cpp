// src/core/material/material.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Material implementations

#include "material.hpp"
#include "disney_brdf.hpp"
#include "../texture/texture.hpp"
#include "../math/vec2.hpp"
#include "../sampler/sampler.hpp"
#include "../scene/scene.hpp"
#include "../common.hpp"
#include <algorithm>
#include <cmath>

namespace photon {

// DiffuseMaterial implementation
DiffuseMaterial::DiffuseMaterial(const Color3& albedo) : m_albedo(albedo) {
}

Color3 DiffuseMaterial::f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    // Lambertian BRDF: albedo / π
    return m_albedo / M_PI;
}

BSDFSample DiffuseMaterial::sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const {
    // Cosine-weighted hemisphere sampling
    float u1 = sampler.get1D();
    float u2 = sampler.get1D();
    
    float cosTheta = std::sqrt(u1);
    float sinTheta = std::sqrt(1.0f - u1);
    float phi = 2.0f * M_PI * u2;
    
    Vec3 localWi(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);
    
    // Transform to world space
    Normal3 n = isect.n;
    Vec3 nt = (std::abs(n.x) > 0.1f) ? Vec3(0, 1, 0) : Vec3(1, 0, 0);
    Vec3 tangent = n.cross(nt).normalized();
    Vec3 bitangent = n.cross(tangent);
    
    Vec3 wi = localWi.x * tangent + localWi.y * bitangent + localWi.z * n;
    
    float pdf = cosTheta / M_PI;
    Color3 f = m_albedo / M_PI;
    
    return BSDFSample(f, wi, pdf, false);
}

float DiffuseMaterial::pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    float cosTheta = std::max(0.0f, wi.dot(isect.n));
    return cosTheta / M_PI;
}

// MirrorMaterial implementation
MirrorMaterial::MirrorMaterial(const Color3& reflectance) : m_reflectance(reflectance) {
}

Color3 MirrorMaterial::f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    // Delta function - only perfect reflection contributes
    return Color3(0);
}

BSDFSample MirrorMaterial::sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const {
    // Perfect reflection: wi = reflect(-wo, n)
    Vec3 wi = Vec3::reflect(-wo, isect.n);
    
    // Delta distribution has infinite PDF, but we return 1 and handle it specially
    return BSDFSample(m_reflectance, wi, 1.0f, true);
}

float MirrorMaterial::pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    // Delta distribution
    return 0.0f;
}

// EmissiveMaterial implementation
EmissiveMaterial::EmissiveMaterial(const Color3& emission) : m_emission(emission) {
}

Color3 EmissiveMaterial::f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    // Emissive materials don't reflect light
    return Color3(0);
}

BSDFSample EmissiveMaterial::sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const {
    // No scattering
    return BSDFSample();
}

float EmissiveMaterial::pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    return 0.0f;
}

Color3 EmissiveMaterial::Le(const Intersection& isect, const Vec3& wo) const {
    // Emit light in all directions
    return m_emission;
}

// PlasticMaterial implementation
PlasticMaterial::PlasticMaterial(const Color3& diffuse, const Color3& specular, float roughness)
    : m_diffuse(diffuse), m_specular(specular), m_roughness(std::clamp(roughness, 0.01f, 1.0f)) {
}

Color3 PlasticMaterial::f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    float cosTheta = std::max(0.0f, wi.dot(isect.n));
    if (cosTheta <= 0.0f) return Color3(0);
    
    // Diffuse component (Lambertian)
    Color3 diffuse = m_diffuse / M_PI;
    
    // Specular component (simplified Blinn-Phong)
    Vec3 h = (wo + wi).normalized();
    float cosAlpha = std::max(0.0f, h.dot(isect.n));
    float shininess = 2.0f / (m_roughness * m_roughness) - 2.0f;
    Color3 specular = m_specular * std::pow(cosAlpha, shininess) * (shininess + 2.0f) / (2.0f * M_PI);
    
    // Fresnel term (simplified)
    float F = fresnel(wo.dot(isect.n));
    
    return diffuse * (1.0f - F) + specular * F;
}

BSDFSample PlasticMaterial::sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const {
    // Simple approach: randomly choose between diffuse and specular
    float F = fresnel(wo.dot(isect.n));
    
    if (sampler.get1D() < F) {
        // Sample specular (simplified - just use diffuse for now)
        return DiffuseMaterial(m_specular).sample(isect, wo, sampler);
    } else {
        // Sample diffuse
        return DiffuseMaterial(m_diffuse).sample(isect, wo, sampler);
    }
}

float PlasticMaterial::pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    float cosTheta = std::max(0.0f, wi.dot(isect.n));
    return cosTheta / M_PI; // Simplified
}

float PlasticMaterial::fresnel(float cosTheta) const {
    // Schlick's approximation for dielectric Fresnel
    float F0 = 0.04f; // Plastic
    return F0 + (1.0f - F0) * std::pow(1.0f - cosTheta, 5.0f);
}

float PlasticMaterial::distribution(const Vec3& wh, float alpha) const {
    // GGX/Trowbridge-Reitz distribution
    float cosTheta = wh.z;
    if (cosTheta <= 0.0f) return 0.0f;
    
    float alpha2 = alpha * alpha;
    float cosTheta2 = cosTheta * cosTheta;
    float tanTheta2 = (1.0f - cosTheta2) / cosTheta2;
    
    return alpha2 / (M_PI * cosTheta2 * cosTheta2 * (alpha2 + tanTheta2) * (alpha2 + tanTheta2));
}

float PlasticMaterial::geometry(const Vec3& wo, const Vec3& wi, const Vec3& wh, float alpha) const {
    // Smith masking-shadowing function (simplified)
    return 1.0f; // Placeholder
}

// PBRMaterial implementation
PBRMaterial::PBRMaterial() {
    // Default to plastic-like material
    DisneyBRDFParams params = DisneyMaterialPresets::createPlastic();
    m_brdf.setParameters(params);
}

PBRMaterial::PBRMaterial(const DisneyBRDFParams& params) {
    m_brdf.setParameters(params);
}

PBRMaterial::PBRMaterial(const Color3& baseColor, float metallic, float roughness) {
    DisneyBRDFParams params;
    params.baseColor = baseColor;
    params.metallic = metallic;
    params.roughness = roughness;
    params.validate();
    m_brdf.setParameters(params);
}

Color3 PBRMaterial::f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    // Use texture-aware evaluation if textures are present
    if (m_baseColorTexture || m_metallicTexture || m_roughnessTexture) {
        return evaluateWithTextures(isect, wo, wi);
    }

    return m_brdf.eval(wo, wi, isect.n);
}

BSDFSample PBRMaterial::sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const {
    // Use texture-aware sampling if textures are present
    if (m_baseColorTexture || m_metallicTexture || m_roughnessTexture) {
        return sampleWithTextures(isect, wo, sampler);
    }

    Vec3 wi;
    float pdf;
    Color3 f = m_brdf.sample(wo, isect.n, sampler, wi, pdf);

    return BSDFSample(f, wi, pdf, false);
}

float PBRMaterial::pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    // For PDF calculation, we can use the base BRDF since it doesn't change the distribution
    return m_brdf.pdf(wo, wi, isect.n);
}

void PBRMaterial::setParameters(const DisneyBRDFParams& params) {
    m_brdf.setParameters(params);
}

const DisneyBRDFParams& PBRMaterial::getParameters() const {
    return m_brdf.getParameters();
}

void PBRMaterial::setBaseColor(const Color3& color) {
    DisneyBRDFParams params = m_brdf.getParameters();
    params.baseColor = color;
    m_brdf.setParameters(params);
}

void PBRMaterial::setMetallic(float metallic) {
    DisneyBRDFParams params = m_brdf.getParameters();
    params.metallic = std::clamp(metallic, 0.0f, 1.0f);
    m_brdf.setParameters(params);
}

void PBRMaterial::setRoughness(float roughness) {
    DisneyBRDFParams params = m_brdf.getParameters();
    params.roughness = std::clamp(roughness, 0.0f, 1.0f);
    m_brdf.setParameters(params);
}

void PBRMaterial::setSpecular(float specular) {
    DisneyBRDFParams params = m_brdf.getParameters();
    params.specular = std::clamp(specular, 0.0f, 1.0f);
    m_brdf.setParameters(params);
}

const Color3& PBRMaterial::getBaseColor() const {
    return m_brdf.getParameters().baseColor;
}

float PBRMaterial::getMetallic() const {
    return m_brdf.getParameters().metallic;
}

float PBRMaterial::getRoughness() const {
    return m_brdf.getParameters().roughness;
}

std::shared_ptr<PBRMaterial> PBRMaterial::createPreset(const std::string& preset, const Color3& color) {
    DisneyBRDFParams params;

    if (preset == "plastic") {
        params = DisneyMaterialPresets::createPlastic(color);
    } else if (preset == "metal") {
        params = DisneyMaterialPresets::createMetal(color);
    } else if (preset == "glass") {
        params = DisneyMaterialPresets::createGlass(color);
    } else if (preset == "wood") {
        params = DisneyMaterialPresets::createWood(color);
    } else if (preset == "fabric") {
        params = DisneyMaterialPresets::createFabric(color);
    } else if (preset == "skin") {
        params = DisneyMaterialPresets::createSkin(color);
    } else if (preset == "ceramic") {
        params = DisneyMaterialPresets::createCeramic(color);
    } else if (preset == "rubber") {
        params = DisneyMaterialPresets::createRubber(color);
    } else {
        // Default to plastic
        params = DisneyMaterialPresets::createPlastic(color);
    }

    return std::make_shared<PBRMaterial>(params);
}

bool PBRMaterial::validateEnergyConservation() const {
    return DisneyMaterialPresets::validateEnergyConservation(m_brdf.getParameters());
}

void PBRMaterial::setBaseColorTexture(std::shared_ptr<Texture> texture) {
    m_baseColorTexture = texture;
}

void PBRMaterial::setMetallicTexture(std::shared_ptr<Texture> texture) {
    m_metallicTexture = texture;
}

void PBRMaterial::setRoughnessTexture(std::shared_ptr<Texture> texture) {
    m_roughnessTexture = texture;
}

void PBRMaterial::setNormalTexture(std::shared_ptr<Texture> texture) {
    m_normalTexture = texture;
}

void PBRMaterial::setSpecularTexture(std::shared_ptr<Texture> texture) {
    m_specularTexture = texture;
}

Color3 PBRMaterial::evaluateWithTextures(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    // Get UV coordinates from intersection
    Vec2 uv(0.0f, 0.0f);
    if (isect.hasUV) {
        uv = isect.getUV();
    }

    // Apply textures to get modified BRDF parameters
    DisneyBRDFParams texturedParams = applyTextures(uv);

    // Create temporary BRDF with textured parameters
    DisneyBRDF texturedBRDF(texturedParams);

    // Apply normal mapping if present
    Vec3 normal = isect.n;
    if (m_normalTexture && isect.hasTangents) {
        normal = applyNormalMapping(uv, isect.n, isect.getTangent(), isect.getBitangent());
    }

    return texturedBRDF.eval(wo, wi, normal);
}

BSDFSample PBRMaterial::sampleWithTextures(const Intersection& isect, const Vec3& wo, Sampler& sampler) const {
    // Get UV coordinates from intersection
    Vec2 uv(0.0f, 0.0f);
    if (isect.hasUV) {
        uv = isect.getUV();
    }

    // Apply textures to get modified BRDF parameters
    DisneyBRDFParams texturedParams = applyTextures(uv);

    // Create temporary BRDF with textured parameters
    DisneyBRDF texturedBRDF(texturedParams);

    // Apply normal mapping if present
    Vec3 normal = isect.n;
    if (m_normalTexture && isect.hasTangents) {
        normal = applyNormalMapping(uv, isect.n, isect.getTangent(), isect.getBitangent());
    }

    Vec3 wi;
    float pdf;
    Color3 f = texturedBRDF.sample(wo, normal, sampler, wi, pdf);

    return BSDFSample(f, wi, pdf, false);
}

DisneyBRDFParams PBRMaterial::applyTextures(const Vec2& uv) const {
    DisneyBRDFParams params = m_brdf.getParameters();

    // Apply base color texture
    if (m_baseColorTexture) {
        Color3 textureColor = m_baseColorTexture->sample(uv);
        params.baseColor = params.baseColor * textureColor;
    }

    // Apply metallic texture
    if (m_metallicTexture) {
        float metallicValue = m_metallicTexture->sampleFloat(uv);
        params.metallic = params.metallic * metallicValue;
    }

    // Apply roughness texture
    if (m_roughnessTexture) {
        float roughnessValue = m_roughnessTexture->sampleFloat(uv);
        params.roughness = params.roughness * roughnessValue;
    }

    // Apply specular texture
    if (m_specularTexture) {
        float specularValue = m_specularTexture->sampleFloat(uv);
        params.specular = params.specular * specularValue;
    }

    // Validate parameters after texture application
    params.validate();

    return params;
}

// Texture setters implementation
void PBRMaterial::setBumpTexture(std::shared_ptr<Texture> texture) {
    m_bumpTexture = texture;
}

void PBRMaterial::setBumpIntensity(float intensity) {
    m_bumpIntensity = std::max(0.0f, std::min(1.0f, intensity));
}

Vec3 PBRMaterial::applyNormalMapping(const Vec2& uv, const Vec3& normal, const Vec3& tangent, const Vec3& bitangent) const {
    if (!m_normalTexture) {
        return normal;
    }

    // Sample normal map
    Color3 normalMapColor = m_normalTexture->sample(uv);

    // Convert from [0,1] to [-1,1] range
    Vec3 normalMapVector(
        normalMapColor.r * 2.0f - 1.0f,
        normalMapColor.g * 2.0f - 1.0f,
        normalMapColor.b * 2.0f - 1.0f
    );

    // Transform from tangent space to world space
    Vec3 worldNormal = tangent * normalMapVector.x +
                      bitangent * normalMapVector.y +
                      normal * normalMapVector.z;

    return worldNormal.normalized();
}

Vec3 PBRMaterial::applyBumpMapping(const Vec2& uv, const Vec3& normal, const Vec3& tangent, const Vec3& bitangent) const {
    if (!m_bumpTexture || m_bumpIntensity <= 0.0f) {
        return normal;
    }

    // Sample height map at current position
    float height = m_bumpTexture->sampleFloat(uv);

    // Calculate gradient by sampling neighboring pixels
    const float epsilon = 0.001f; // Small offset for gradient calculation

    // Sample neighboring heights for gradient calculation
    float heightU = m_bumpTexture->sampleFloat(Vec2(uv.x + epsilon, uv.y));
    float heightV = m_bumpTexture->sampleFloat(Vec2(uv.x, uv.y + epsilon));

    // Calculate gradients (dH/du, dH/dv)
    float gradientU = (heightU - height) / epsilon;
    float gradientV = (heightV - height) / epsilon;

    // Apply bump intensity scaling
    gradientU *= m_bumpIntensity;
    gradientV *= m_bumpIntensity;

    // Create perturbation vector in tangent space
    Vec3 perturbation = tangent * gradientU + bitangent * gradientV;

    // Apply perturbation to normal
    Vec3 bumpedNormal = normal - perturbation;

    return bumpedNormal.normalized();
}

Vec3 PBRMaterial::applySurfaceMapping(const Vec2& uv, const Vec3& normal, const Vec3& tangent, const Vec3& bitangent) const {
    // Start with original normal
    Vec3 resultNormal = normal;

    // Apply normal mapping first (if present)
    if (m_normalTexture) {
        resultNormal = applyNormalMapping(uv, resultNormal, tangent, bitangent);
    }

    // Apply bump mapping on top (if present)
    if (m_bumpTexture && m_bumpIntensity > 0.0f) {
        resultNormal = applyBumpMapping(uv, resultNormal, tangent, bitangent);
    }

    return resultNormal.normalized();
}

} // namespace photon
