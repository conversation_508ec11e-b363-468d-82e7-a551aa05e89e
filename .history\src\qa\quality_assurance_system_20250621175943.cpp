// src/qa/quality_assurance_system.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Quality Assurance System Implementation

#include "quality_assurance_system.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <thread>
#include <future>
#include <iomanip>

namespace photon {
namespace qa {

// TestResult Implementation
std::string TestResult::getStatusString() const {
    switch (status) {
        case TestStatus::PASSED: return "PASSED";
        case TestStatus::FAILED: return "FAILED";
        case TestStatus::SKIPPED: return "SKIPPED";
        case TestStatus::TIMEOUT: return "TIMEOUT";
        case TestStatus::ERROR: return "ERROR";
        case TestStatus::NOT_RUN: return "NOT_RUN";
        default: return "UNKNOWN";
    }
}

std::string TestResult::getSeverityString() const {
    switch (severity) {
        case TestSeverity::CRITICAL: return "CRITICAL";
        case TestSeverity::HIGH: return "HIGH";
        case TestSeverity::MEDIUM: return "MEDIUM";
        case TestSeverity::LOW: return "LOW";
        case TestSeverity::INFORMATIONAL: return "INFO";
        default: return "UNKNOWN";
    }
}

std::string TestResult::getCategoryString() const {
    switch (category) {
        case TestCategory::UNIT: return "UNIT";
        case TestCategory::INTEGRATION: return "INTEGRATION";
        case TestCategory::PERFORMANCE: return "PERFORMANCE";
        case TestCategory::REGRESSION: return "REGRESSION";
        case TestCategory::MEMORY: return "MEMORY";
        case TestCategory::GPU: return "GPU";
        case TestCategory::STRESS: return "STRESS";
        case TestCategory::VALIDATION: return "VALIDATION";
        case TestCategory::COMPATIBILITY: return "COMPATIBILITY";
        default: return "UNKNOWN";
    }
}

// TestContext Implementation
TestContext::TestContext(const std::string& name, TestCategory cat)
    : test_name(name), category(cat), start_time(std::chrono::steady_clock::now()) {
    
    // Initialize memory monitoring
    initial_memory_usage = AdvancedMemoryManager::getInstance().getStatistics().current_usage;
    
    // Initialize profiler
    profiler = std::make_unique<PerformanceProfiler>();
    profiler->initialize();
}

TestContext::~TestContext() {
    if (profiler) {
        profiler->shutdown();
    }
}

void TestContext::startProfiling() {
    if (profiler) {
        profiler->beginFrame();
    }
}

void TestContext::stopProfiling() {
    if (profiler) {
        profiler->endFrame();
    }
}

TestResult TestContext::finalize(TestStatus status, const std::string& error) {
    auto end_time = std::chrono::steady_clock::now();
    auto execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    TestResult result(test_name, "", category, TestSeverity::HIGH);
    result.status = status;
    result.execution_time = execution_time;
    result.error_message = error;
    result.output_log = output_stream.str();
    
    // Calculate performance metrics
    if (profiler) {
        result.performance_score = profiler->getFPS() > 0 ? std::min(1.0, profiler->getFPS() / 60.0) : 1.0;
    }
    
    // Calculate memory usage
    auto current_memory = AdvancedMemoryManager::getInstance().getStatistics().current_usage;
    result.memory_usage_bytes = current_memory - initial_memory_usage;
    
    return result;
}

// QualityAssuranceSystem Implementation
QualityAssuranceSystem::QualityAssuranceSystem() : m_initialized(false) {}

QualityAssuranceSystem::~QualityAssuranceSystem() {
    shutdown();
}

bool QualityAssuranceSystem::initialize(const TestSuiteConfig& config) {
    if (m_initialized) return true;
    
    m_config = config;
    
    // Create output directory
    createOutputDirectory();
    
    // Initialize built-in tests
    registerBuiltinTests();
    
    m_initialized = true;
    return true;
}

void QualityAssuranceSystem::shutdown() {
    if (!m_initialized) return;
    
    m_registered_tests.clear();
    m_test_results.clear();
    m_custom_validators.clear();
    
    m_initialized = false;
}

void QualityAssuranceSystem::registerTest(const std::string& name, const std::string& description,
                                         TestCategory category, TestSeverity severity,
                                         std::function<bool(TestContext&)> test_function) {
    RegisteredTest test;
    test.name = name;
    test.description = description;
    test.category = category;
    test.severity = severity;
    test.test_function = test_function;
    
    m_registered_tests.push_back(test);
}

bool QualityAssuranceSystem::runAllTests() {
    if (!m_initialized) return false;
    
    std::cout << "=== PhotonRender Quality Assurance System ===" << std::endl;
    std::cout << "Running " << m_registered_tests.size() << " tests..." << std::endl;
    
    m_test_results.clear();
    m_test_results.reserve(m_registered_tests.size());
    
    auto start_time = std::chrono::steady_clock::now();
    
    // Filter tests based on configuration
    std::vector<RegisteredTest> tests_to_run;
    for (const auto& test : m_registered_tests) {
        bool should_run = false;
        
        switch (test.category) {
            case TestCategory::UNIT: should_run = m_config.run_unit_tests; break;
            case TestCategory::INTEGRATION: should_run = m_config.run_integration_tests; break;
            case TestCategory::PERFORMANCE: should_run = m_config.run_performance_tests; break;
            case TestCategory::REGRESSION: should_run = m_config.run_regression_tests; break;
            case TestCategory::MEMORY: should_run = m_config.run_memory_tests; break;
            case TestCategory::GPU: should_run = m_config.run_gpu_tests; break;
            case TestCategory::STRESS: should_run = m_config.run_stress_tests; break;
            case TestCategory::VALIDATION: should_run = m_config.run_validation_tests; break;
            case TestCategory::COMPATIBILITY: should_run = m_config.run_compatibility_tests; break;
        }
        
        if (should_run) {
            tests_to_run.push_back(test);
        }
    }
    
    std::cout << "Filtered to " << tests_to_run.size() << " tests based on configuration." << std::endl;
    
    // Run tests (potentially in parallel)
    if (m_config.max_parallel_tests > 1) {
        // Parallel execution
        std::vector<std::future<TestResult>> futures;
        
        for (const auto& test : tests_to_run) {
            if (futures.size() >= static_cast<size_t>(m_config.max_parallel_tests)) {
                // Wait for one to complete
                for (auto it = futures.begin(); it != futures.end(); ++it) {
                    if (it->wait_for(std::chrono::milliseconds(10)) == std::future_status::ready) {
                        m_test_results.push_back(it->get());
                        futures.erase(it);
                        break;
                    }
                }
            }
            
            futures.push_back(std::async(std::launch::async, [this, test]() {
                return executeTest(test);
            }));
        }
        
        // Wait for remaining tests
        for (auto& future : futures) {
            m_test_results.push_back(future.get());
        }
    } else {
        // Sequential execution
        for (const auto& test : tests_to_run) {
            TestResult result = executeTest(test);
            m_test_results.push_back(result);
            
            // Stop on first failure if configured
            if (m_config.stop_on_first_failure && !result.isPassed()) {
                std::cout << "Stopping on first failure: " << test.name << std::endl;
                break;
            }
        }
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Generate summary
    TestSummary summary = getTestSummary();
    summary.total_execution_time = total_time;
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Total tests: " << summary.total_tests << std::endl;
    std::cout << "Passed: " << summary.passed_tests << std::endl;
    std::cout << "Failed: " << summary.failed_tests << std::endl;
    std::cout << "Skipped: " << summary.skipped_tests << std::endl;
    std::cout << "Critical failures: " << summary.critical_failures << std::endl;
    std::cout << "Overall score: " << std::fixed << std::setprecision(1) << (summary.overall_score * 100.0) << "%" << std::endl;
    std::cout << "Execution time: " << total_time.count() << "ms" << std::endl;
    std::cout << "All critical tests passed: " << (summary.all_critical_passed ? "YES" : "NO") << std::endl;
    
    // Generate reports if configured
    if (m_config.generate_detailed_reports) {
        generateReports();
    }
    
    return summary.all_critical_passed && summary.failed_tests == 0;
}

TestResult QualityAssuranceSystem::executeTest(const RegisteredTest& test) {
    std::cout << "Running " << test.name << "... ";
    std::cout.flush();
    
    TestContext context(test.name, test.category);
    context.startProfiling();
    
    bool test_passed = false;
    std::string error_message;
    
    try {
        // Set timeout
        auto future = std::async(std::launch::async, [&]() {
            return test.test_function(context);
        });
        
        if (future.wait_for(m_config.test_timeout) == std::future_status::timeout) {
            error_message = "Test timed out after " + std::to_string(m_config.test_timeout.count()) + " seconds";
            context.stopProfiling();
            TestResult result = context.finalize(TestStatus::TIMEOUT, error_message);
            result.test_description = test.description;
            result.severity = test.severity;
            std::cout << "TIMEOUT" << std::endl;
            return result;
        }
        
        test_passed = future.get();
        
    } catch (const std::exception& e) {
        error_message = "Exception: " + std::string(e.what());
        test_passed = false;
    } catch (...) {
        error_message = "Unknown exception occurred";
        test_passed = false;
    }
    
    context.stopProfiling();
    
    TestStatus status = test_passed ? TestStatus::PASSED : TestStatus::FAILED;
    if (!error_message.empty() && status != TestStatus::TIMEOUT) {
        status = TestStatus::ERROR;
    }
    
    TestResult result = context.finalize(status, error_message);
    result.test_description = test.description;
    result.severity = test.severity;
    
    // Validate result
    if (test_passed) {
        test_passed = validatePerformance(result) && 
                     validateMemoryUsage(result) && 
                     validateOutput(result);
        
        if (!test_passed) {
            result.status = TestStatus::FAILED;
            if (result.error_message.empty()) {
                result.error_message = "Validation failed";
            }
        }
    }
    
    // Check regression baseline
    if (test_passed && !m_regression_baselines.empty()) {
        if (!checkRegressionBaseline(result)) {
            result.status = TestStatus::FAILED;
            result.error_message += " (Regression detected)";
            test_passed = false;
        }
    }
    
    std::cout << result.getStatusString() << std::endl;
    
    // Save test artifacts if configured
    if (m_config.save_test_artifacts) {
        saveTestArtifacts(result);
    }
    
    return result;
}

QualityAssuranceSystem::TestSummary QualityAssuranceSystem::getTestSummary() const {
    TestSummary summary;
    summary.total_tests = static_cast<int>(m_test_results.size());
    summary.passed_tests = 0;
    summary.failed_tests = 0;
    summary.skipped_tests = 0;
    summary.critical_failures = 0;
    summary.overall_score = 0.0;
    summary.total_execution_time = std::chrono::milliseconds(0);
    summary.all_critical_passed = true;
    
    if (summary.total_tests == 0) return summary;
    
    double total_score = 0.0;
    
    for (const auto& result : m_test_results) {
        switch (result.status) {
            case TestStatus::PASSED:
                summary.passed_tests++;
                total_score += 1.0;
                break;
            case TestStatus::FAILED:
            case TestStatus::ERROR:
                summary.failed_tests++;
                if (result.isCritical()) {
                    summary.critical_failures++;
                    summary.all_critical_passed = false;
                }
                break;
            case TestStatus::SKIPPED:
                summary.skipped_tests++;
                total_score += 0.5; // Partial credit for skipped tests
                break;
            case TestStatus::TIMEOUT:
                summary.failed_tests++;
                if (result.isCritical()) {
                    summary.critical_failures++;
                    summary.all_critical_passed = false;
                }
                break;
            default:
                break;
        }
        
        summary.total_execution_time += result.execution_time;
    }
    
    summary.overall_score = total_score / summary.total_tests;
    
    return summary;
}

bool QualityAssuranceSystem::validatePerformance(const TestResult& result) {
    return result.performance_score >= m_config.min_performance_score;
}

bool QualityAssuranceSystem::validateMemoryUsage(const TestResult& result) {
    size_t max_bytes = m_config.max_memory_usage_mb * 1024 * 1024;
    return result.memory_usage_bytes <= max_bytes;
}

bool QualityAssuranceSystem::validateOutput(const TestResult& result) {
    auto it = m_custom_validators.find(result.test_name);
    if (it != m_custom_validators.end()) {
        return it->second(result.output_log);
    }
    return result.output_valid;
}

void QualityAssuranceSystem::createOutputDirectory() {
    try {
        std::filesystem::create_directories(m_config.output_directory);
    } catch (const std::exception& e) {
        std::cerr << "Failed to create output directory: " << e.what() << std::endl;
    }
}

void QualityAssuranceSystem::saveTestArtifacts(const TestResult& result) {
    try {
        std::string test_dir = m_config.output_directory + "/" + result.test_name;
        std::filesystem::create_directories(test_dir);
        
        // Save output log
        std::ofstream log_file(test_dir + "/output.log");
        log_file << result.output_log;
        log_file.close();
        
        // Save error log if there's an error
        if (!result.error_message.empty()) {
            std::ofstream error_file(test_dir + "/error.log");
            error_file << result.error_message;
            error_file.close();
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to save test artifacts for " << result.test_name << ": " << e.what() << std::endl;
    }
}

void QualityAssuranceSystem::registerBuiltinTests() {
    // Unit tests
    QA_REGISTER_UNIT_TEST(*this, "math_library", "Test math library functionality", BuiltinTests::testMathLibrary);
    QA_REGISTER_UNIT_TEST(*this, "scene_management", "Test scene management system", BuiltinTests::testSceneManagement);
    QA_REGISTER_UNIT_TEST(*this, "memory_management", "Test advanced memory management", BuiltinTests::testMemoryManagement);
    QA_REGISTER_UNIT_TEST(*this, "adaptive_sampling", "Test adaptive sampling system", BuiltinTests::testAdaptiveSampling);
    QA_REGISTER_UNIT_TEST(*this, "performance_profiling", "Test performance profiling system", BuiltinTests::testPerformanceProfiling);

    // Integration tests
    QA_REGISTER_TEST(*this, "rendering_pipeline", "Test complete rendering pipeline",
                     TestCategory::INTEGRATION, TestSeverity::CRITICAL, BuiltinTests::testRenderingPipeline);
    QA_REGISTER_TEST(*this, "material_system", "Test material system integration",
                     TestCategory::INTEGRATION, TestSeverity::HIGH, BuiltinTests::testMaterialSystem);
    QA_REGISTER_TEST(*this, "lighting_system", "Test lighting system integration",
                     TestCategory::INTEGRATION, TestSeverity::HIGH, BuiltinTests::testLightingSystem);

    // Performance tests
    QA_REGISTER_PERFORMANCE_TEST(*this, "raytracing_performance", "Test ray tracing performance",
                                 BuiltinTests::performanceTestRayTracing);
    QA_REGISTER_PERFORMANCE_TEST(*this, "memory_allocation_performance", "Test memory allocation performance",
                                 BuiltinTests::performanceTestMemoryAllocation);
    QA_REGISTER_PERFORMANCE_TEST(*this, "gpu_throughput_performance", "Test GPU throughput performance",
                                 BuiltinTests::performanceTestGPUThroughput);

    // Regression tests
    QA_REGISTER_REGRESSION_TEST(*this, "render_output_regression", "Test render output consistency",
                                BuiltinTests::regressionTestRenderOutput);
    QA_REGISTER_REGRESSION_TEST(*this, "performance_regression", "Test performance regression",
                                BuiltinTests::regressionTestPerformance);
    QA_REGISTER_REGRESSION_TEST(*this, "memory_usage_regression", "Test memory usage regression",
                                BuiltinTests::regressionTestMemoryUsage);

    // GPU tests
    QA_REGISTER_TEST(*this, "gpu_kernels", "Test GPU kernel functionality",
                     TestCategory::GPU, TestSeverity::HIGH, BuiltinTests::testGPUKernels);

    // Stress tests
    QA_REGISTER_TEST(*this, "large_scene_stress", "Test large scene handling",
                     TestCategory::STRESS, TestSeverity::MEDIUM, BuiltinTests::stressTestLargeScene);
    QA_REGISTER_TEST(*this, "high_sample_stress", "Test high sample count rendering",
                     TestCategory::STRESS, TestSeverity::MEDIUM, BuiltinTests::stressTestHighSampleCount);
    QA_REGISTER_TEST(*this, "memory_pressure_stress", "Test memory pressure handling",
                     TestCategory::STRESS, TestSeverity::MEDIUM, BuiltinTests::stressTestMemoryPressure);

    // Validation tests
    QA_REGISTER_TEST(*this, "image_io", "Test image I/O functionality",
                     TestCategory::VALIDATION, TestSeverity::HIGH, BuiltinTests::testImageIO);
}

void QualityAssuranceSystem::generateReports() {
    if (m_config.generate_html_report) {
        generateHTMLReport();
    }

    if (m_config.generate_junit_xml) {
        generateJUnitXMLReport();
    }

    generatePerformanceReport();
}

void QualityAssuranceSystem::generateHTMLReport(const std::string& filename) {
    std::string filepath = m_config.output_directory + "/" + filename;
    std::ofstream file(filepath);

    if (!file.is_open()) {
        std::cerr << "Failed to create HTML report: " << filepath << std::endl;
        return;
    }

    TestSummary summary = getTestSummary();

    file << "<!DOCTYPE html>\n<html>\n<head>\n";
    file << "<title>PhotonRender QA Report</title>\n";
    file << "<style>\n";
    file << "body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #e0e0e0; }\n";
    file << ".header { background: #2d2d2d; padding: 20px; border-radius: 8px; margin-bottom: 20px; }\n";
    file << ".summary { background: #2d2d2d; padding: 15px; margin: 10px 0; border-radius: 8px; }\n";
    file << ".test-result { margin: 5px 0; padding: 10px; border-radius: 4px; }\n";
    file << ".passed { background: #2d4d2d; border-left: 4px solid #4caf50; }\n";
    file << ".failed { background: #4d2d2d; border-left: 4px solid #f44336; }\n";
    file << ".skipped { background: #4d4d2d; border-left: 4px solid #ff9800; }\n";
    file << ".critical { font-weight: bold; }\n";
    file << "h1, h2, h3 { color: #74c0fc; }\n";
    file << ".metric { display: inline-block; margin: 10px; padding: 10px; background: #3d3d3d; border-radius: 4px; }\n";
    file << "</style>\n</head>\n<body>\n";

    // Header
    file << "<div class='header'>\n";
    file << "<h1>PhotonRender Quality Assurance Report</h1>\n";
    file << "<p>Generated: " << std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count() << "</p>\n";
    file << "</div>\n";

    // Summary
    file << generateSummaryHTML(summary);

    // Test results by category
    std::unordered_map<TestCategory, std::vector<TestResult>> results_by_category;
    for (const auto& result : m_test_results) {
        results_by_category[result.category].push_back(result);
    }

    for (const auto& category_pair : results_by_category) {
        file << "<div class='summary'>\n";
        file << "<h2>" << category_pair.second[0].getCategoryString() << " Tests</h2>\n";

        for (const auto& result : category_pair.second) {
            file << generateTestResultHTML(result);
        }

        file << "</div>\n";
    }

    // Performance chart
    file << generatePerformanceChartHTML();

    file << "</body>\n</html>";
    file.close();

    std::cout << "HTML report generated: " << filepath << std::endl;
}

std::string QualityAssuranceSystem::generateSummaryHTML(const TestSummary& summary) {
    std::stringstream ss;

    ss << "<div class='summary'>\n<h2>Test Summary</h2>\n";
    ss << "<div class='metric'>Total Tests: " << summary.total_tests << "</div>\n";
    ss << "<div class='metric'>Passed: " << summary.passed_tests << "</div>\n";
    ss << "<div class='metric'>Failed: " << summary.failed_tests << "</div>\n";
    ss << "<div class='metric'>Skipped: " << summary.skipped_tests << "</div>\n";
    ss << "<div class='metric'>Critical Failures: " << summary.critical_failures << "</div>\n";
    ss << "<div class='metric'>Overall Score: " << std::fixed << std::setprecision(1)
       << (summary.overall_score * 100.0) << "%</div>\n";
    ss << "<div class='metric'>Execution Time: " << summary.total_execution_time.count() << "ms</div>\n";
    ss << "<div class='metric'>All Critical Passed: " << (summary.all_critical_passed ? "YES" : "NO") << "</div>\n";
    ss << "</div>\n";

    return ss.str();
}

std::string QualityAssuranceSystem::generateTestResultHTML(const TestResult& result) {
    std::stringstream ss;

    std::string css_class = "test-result ";
    switch (result.status) {
        case TestStatus::PASSED: css_class += "passed"; break;
        case TestStatus::FAILED:
        case TestStatus::ERROR:
        case TestStatus::TIMEOUT: css_class += "failed"; break;
        case TestStatus::SKIPPED: css_class += "skipped"; break;
        default: css_class += "failed"; break;
    }

    if (result.isCritical()) {
        css_class += " critical";
    }

    ss << "<div class='" << css_class << "'>\n";
    ss << "<h3>" << result.test_name << " - " << result.getStatusString() << "</h3>\n";
    ss << "<p>" << result.test_description << "</p>\n";
    ss << "<p><strong>Category:</strong> " << result.getCategoryString() << " | ";
    ss << "<strong>Severity:</strong> " << result.getSeverityString() << " | ";
    ss << "<strong>Time:</strong> " << result.execution_time.count() << "ms</p>\n";

    if (result.performance_score > 0) {
        ss << "<p><strong>Performance Score:</strong> " << std::fixed << std::setprecision(3)
           << result.performance_score << "</p>\n";
    }

    if (result.memory_usage_bytes > 0) {
        ss << "<p><strong>Memory Usage:</strong> " << (result.memory_usage_bytes / 1024 / 1024) << " MB</p>\n";
    }

    if (!result.error_message.empty()) {
        ss << "<p><strong>Error:</strong> " << result.error_message << "</p>\n";
    }

    if (!result.warnings.empty()) {
        ss << "<p><strong>Warnings:</strong></p>\n<ul>\n";
        for (const auto& warning : result.warnings) {
            ss << "<li>" << warning << "</li>\n";
        }
        ss << "</ul>\n";
    }

    ss << "</div>\n";

    return ss.str();
}

// AutomatedTestRunner Implementation
int AutomatedTestRunner::runAutomatedTests(int argc, char* argv[]) {
    std::cout << "PhotonRender Automated Test Runner" << std::endl;

    // Setup test environment
    if (!setupTestEnvironment()) {
        std::cerr << "Failed to setup test environment" << std::endl;
        return 1;
    }

    // Parse command line arguments
    TestSuiteConfig config = parseCommandLineArgs(argc, argv);

    // Initialize QA system
    QualityAssuranceSystem qa_system;
    if (!qa_system.initialize(config)) {
        std::cerr << "Failed to initialize QA system" << std::endl;
        cleanupTestEnvironment();
        return 1;
    }

    // Run tests
    bool success = qa_system.runAllTests();

    // Generate exit code
    TestSummary summary = qa_system.getTestSummary();
    int exit_code = generateExitCode(summary);

    // Cleanup
    qa_system.shutdown();
    cleanupTestEnvironment();

    return exit_code;
}

TestSuiteConfig AutomatedTestRunner::parseCommandLineArgs(int argc, char* argv[]) {
    TestSuiteConfig config;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--no-unit") config.run_unit_tests = false;
        else if (arg == "--no-integration") config.run_integration_tests = false;
        else if (arg == "--no-performance") config.run_performance_tests = false;
        else if (arg == "--no-regression") config.run_regression_tests = false;
        else if (arg == "--no-memory") config.run_memory_tests = false;
        else if (arg == "--no-gpu") config.run_gpu_tests = false;
        else if (arg == "--stress") config.run_stress_tests = true;
        else if (arg == "--stop-on-failure") config.stop_on_first_failure = true;
        else if (arg == "--parallel" && i + 1 < argc) {
            config.max_parallel_tests = std::atoi(argv[++i]);
        }
        else if (arg == "--output" && i + 1 < argc) {
            config.output_directory = argv[++i];
        }
        else if (arg == "--timeout" && i + 1 < argc) {
            config.test_timeout = std::chrono::seconds(std::atoi(argv[++i]));
        }
    }

    return config;
}

int AutomatedTestRunner::generateExitCode(const QualityAssuranceSystem::TestSummary& summary) {
    if (summary.critical_failures > 0) return 2; // Critical failure
    if (summary.failed_tests > 0) return 1;      // Some failures
    return 0;                                     // All passed
}

bool AutomatedTestRunner::setupTestEnvironment() {
    // Initialize PhotonRender systems
    try {
        AdvancedMemoryManager::getInstance().initialize();
        PerformanceProfiler::getInstance().initialize();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to setup test environment: " << e.what() << std::endl;
        return false;
    }
}

void AutomatedTestRunner::cleanupTestEnvironment() {
    try {
        PerformanceProfiler::getInstance().shutdown();
        AdvancedMemoryManager::getInstance().shutdown();
    } catch (const std::exception& e) {
        std::cerr << "Error during test environment cleanup: " << e.what() << std::endl;
    }
}

} // namespace qa
} // namespace photon
