// src/core/renderer.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Main renderer class implementation
#pragma once

#include <functional>
#include <atomic>

#ifndef PHOTON_USE_SIMPLE_COMMON
#include <tbb/parallel_for.h>
#include <tbb/blocked_range2d.h>
#endif

#include "common.hpp"

#include "math/vec3.hpp"
#include "math/ray.hpp"
#include "scene/scene.hpp"
#include "integrator/integrator.hpp"
#include "sampler/sampler.hpp"
#include "scene/camera.hpp"

namespace photon {

// Forward declarations
class Scene;
class Camera;
class Integrator;
class Sampler;

// Film class for image storage
class Film {
public:
    Film(int width, int height)
        : m_width(width), m_height(height),
          m_pixels(width * height * 3, 0.0f),
          m_sampleCount(width * height, 0) {}

    void addSample(int x, int y, const Color3& color, float weight = 1.0f) {
        if (x < 0 || x >= m_width || y < 0 || y >= m_height) return;

        int idx = (y * m_width + x) * 3;
        m_pixels[idx + 0] += color.x * weight;
        m_pixels[idx + 1] += color.y * weight;
        m_pixels[idx + 2] += color.z * weight;
        m_sampleCount[y * m_width + x] += weight;
    }

    Color3 getPixel(int x, int y) const {
        int idx = (y * m_width + x) * 3;
        float count = m_sampleCount[y * m_width + x];
        if (count == 0) return Color3(0);

        return Color3(
            m_pixels[idx + 0] / count,
            m_pixels[idx + 1] / count,
            m_pixels[idx + 2] / count
        );
    }

    void setPixel(int x, int y, const Color3& color) {
        if (x < 0 || x >= m_width || y < 0 || y >= m_height) return;

        int idx = (y * m_width + x) * 3;
        m_pixels[idx + 0] = color.x;
        m_pixels[idx + 1] = color.y;
        m_pixels[idx + 2] = color.z;
        m_sampleCount[y * m_width + x] = 1.0f; // Set to 1 for direct pixel setting
    }

    const float* getPixels() const { return m_pixels.data(); }
    int getWidth() const { return m_width; }
    int getHeight() const { return m_height; }

private:
    int m_width, m_height;
    std::vector<float> m_pixels;
    std::vector<float> m_sampleCount;
};

// Render settings structure
struct RenderSettings {
    int width = 1920;
    int height = 1080;
    int samplesPerPixel = 100;
    int maxBounces = 8;
    int tileSize = 64;
    bool useGPU = false;
    bool enableDenoising = true;
    float denoisingStrength = 0.8f;
    
    // Adaptive sampling
    bool adaptiveSampling = true;
    float adaptiveThreshold = 0.01f;
    int adaptiveMinSamples = 16;
    int adaptiveMaxSamples = 1024;
};

// Render statistics
struct RenderStats {
    std::atomic<int> renderedTiles{0};
    std::atomic<int> totalTiles{0};
    std::atomic<int> totalSamples{0};
    std::atomic<float> renderTime{0.0f};

    // Delete copy constructor and assignment operator to avoid atomic copy issues
    RenderStats() = default;
    RenderStats(const RenderStats&) = delete;
    RenderStats& operator=(const RenderStats&) = delete;

    float getProgress() const {
        return totalTiles > 0 ? float(renderedTiles) / float(totalTiles) : 0.0f;
    }
};

// Main renderer class
class Renderer {
public:
    using ProgressCallback = std::function<void(float progress, const RenderStats& stats)>;
    using TileCallback = std::function<void(int x, int y, int width, int height, const float* pixels)>;
    
    Renderer();
    ~Renderer();
    
    // Non-copyable
    Renderer(const Renderer&) = delete;
    Renderer& operator=(const Renderer&) = delete;
    
    // Setup
    void setScene(std::shared_ptr<Scene> scene);
    void setCamera(std::shared_ptr<Camera> camera);
    void setIntegrator(std::shared_ptr<Integrator> integrator);
    void setSettings(const RenderSettings& settings);
    
    // Rendering
    void render();
    void renderTile(int tileX, int tileY);
    void stop();
    bool isRendering() const { return m_isRendering; }
    
    // Callbacks
    void setProgressCallback(ProgressCallback callback) { m_progressCallback = callback; }
    void setTileCallback(TileCallback callback) { m_tileCallback = callback; }
    
    // Results
    const float* getPixels() const { return m_film->getPixels(); }
    const RenderStats& getStats() const { return m_stats; }
    const Film& getFilm() const { return *m_film; }

    // Image output
    bool saveImage(const std::string& filename, int quality = 95) const;
    
private:
    // Core components
    std::shared_ptr<Scene> m_scene;
    std::shared_ptr<Camera> m_camera;
    std::shared_ptr<Integrator> m_integrator;
    std::unique_ptr<Film> m_film;
    RenderSettings m_settings;
    
    // Embree device
    RTCDevice m_device;
    
    // State
    std::atomic<bool> m_isRendering{false};
    std::atomic<bool> m_shouldStop{false};
    RenderStats m_stats;
    
    // Callbacks
    ProgressCallback m_progressCallback;
    TileCallback m_tileCallback;
    
    // Internal methods
    void initializeEmbree();
    void buildAccelerationStructure();
    void renderTileInternal(int tileX, int tileY, int tileWidth, int tileHeight);
    Color3 samplePixel(int x, int y, Sampler& sampler);
};

} // namespace photon
