// src/core/denoising/ai_denoiser.cpp
// PhotonRender - AI Denoising System Implementation

#include "ai_denoiser.hpp"
#include "../renderer.hpp"
#include <iostream>
#include <chrono>
#include <algorithm>
#include <cmath>

// Intel OIDN includes (conditional compilation)
#ifdef PHOTON_USE_OIDN
#include <OpenImageDenoise/oidn.hpp>
#endif

namespace photon {

AIDenoiser::AIDenoiser() : m_device(nullptr), m_filter(nullptr) {
}

AIDenoiser::~AIDenoiser() {
    shutdown();
}

bool AIDenoiser::initialize(const DenoisingSettings& settings) {
#ifdef PHOTON_USE_OIDN
    if (m_initialized) {
        return true;
    }
    
    try {
        m_settings = settings;
        
        if (settings.verbose) {
            std::cout << "[AI Denoiser] Initializing Intel OIDN..." << std::endl;
        }
        
        // Initialize device
        if (!initializeDevice()) {
            std::cerr << "[AI Denoiser ERROR] Failed to initialize OIDN device" << std::endl;
            return false;
        }
        
        m_initialized = true;
        
        if (settings.verbose) {
            std::cout << "[AI Denoiser] Intel OIDN initialized successfully" << std::endl;
            std::cout << "[AI Denoiser] Version: " << getVersion() << std::endl;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[AI Denoiser ERROR] Initialization failed: " << e.what() << std::endl;
        return false;
    }
#else
    std::cout << "[AI Denoiser] Intel OIDN not available (compiled without OIDN support)" << std::endl;
    return false;
#endif
}

void AIDenoiser::shutdown() {
#ifdef PHOTON_USE_OIDN
    if (!m_initialized) {
        return;
    }

    try {
        if (m_filter) {
            delete static_cast<oidn::FilterRef*>(m_filter);
            m_filter = nullptr;
        }

        if (m_device) {
            delete static_cast<oidn::DeviceRef*>(m_device);
            m_device = nullptr;
        }

        m_initialized = false;

        if (m_settings.verbose) {
            std::cout << "[AI Denoiser] Intel OIDN shutdown complete" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "[AI Denoiser ERROR] Shutdown error: " << e.what() << std::endl;
    }
#endif
}

bool AIDenoiser::isAvailable() {
#ifdef PHOTON_USE_OIDN
    try {
        oidn::DeviceRef device = oidn::newDevice();
        device.commit();
        return true;
    } catch (...) {
        return false;
    }
#else
    return false;
#endif
}

std::string AIDenoiser::getVersion() {
#ifdef PHOTON_USE_OIDN
    return std::string(OIDN_VERSION_STRING);
#else
    return "Not Available";
#endif
}

bool AIDenoiser::denoise(Film& film, const DenoisingSettings& settings) {
#ifdef PHOTON_USE_OIDN
    if (!m_initialized && !initialize(settings)) {
        return false;
    }
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Reset stats
    m_stats = DenoisingStats{};
    
    try {
        // Convert Film to DenoisingBuffer
        DenoisingBuffer colorBuffer(film.getWidth(), film.getHeight(), 3);
        filmToBuffer(film, colorBuffer);
        
        // Denoise the buffer
        bool success = false;
        if (colorBuffer.width * colorBuffer.height > settings.tileSize * settings.tileSize) {
            success = denoiseTiled(colorBuffer, nullptr, nullptr, settings);
        } else {
            success = denoise(colorBuffer, nullptr, nullptr, settings);
        }
        
        if (success) {
            // Convert back to Film
            bufferToFilm(colorBuffer, film);
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        m_stats.denoisingTime = std::chrono::duration<double>(endTime - startTime).count();
        m_stats.success = success;
        
        if (settings.verbose) {
            std::cout << "[AI Denoiser] Denoising completed in " 
                      << m_stats.denoisingTime * 1000.0 << "ms" << std::endl;
            std::cout << "[AI Denoiser] Performance: " 
                      << m_stats.getPerformanceMPixPerSec(film.getWidth(), film.getHeight()) 
                      << " MPix/sec" << std::endl;
        }
        
        return success;
        
    } catch (const std::exception& e) {
        m_stats.errorMessage = e.what();
        m_stats.success = false;
        std::cerr << "[AI Denoiser ERROR] Denoising failed: " << e.what() << std::endl;
        return false;
    }
#else
    return false;
#endif
}

bool AIDenoiser::denoise(
    DenoisingBuffer& colorBuffer,
    const DenoisingBuffer* albedoBuffer,
    const DenoisingBuffer* normalBuffer,
    const DenoisingSettings& settings
) {
#ifdef PHOTON_USE_OIDN
    if (!m_initialized && !initialize(settings)) {
        return false;
    }
    
    try {
        // Validate buffers
        if (!validateBuffers(colorBuffer, albedoBuffer, normalBuffer)) {
            m_stats.errorMessage = "Invalid buffer configuration";
            return false;
        }
        
        // Create filter for this operation
        if (!createFilter(settings)) {
            m_stats.errorMessage = "Failed to create OIDN filter";
            return false;
        }
        
        // Cast to OIDN types
        oidn::FilterRef* filter = static_cast<oidn::FilterRef*>(m_filter);
        oidn::DeviceRef* device = static_cast<oidn::DeviceRef*>(m_device);

        // Set input buffers
        filter->setImage("color", colorBuffer.data.data(),
                          oidn::Format::Float3, colorBuffer.width, colorBuffer.height);

        if (settings.useAlbedo && albedoBuffer) {
            filter->setImage("albedo", albedoBuffer->data.data(),
                              oidn::Format::Float3, albedoBuffer->width, albedoBuffer->height);
        }

        if (settings.useNormals && normalBuffer) {
            filter->setImage("normal", normalBuffer->data.data(),
                              oidn::Format::Float3, normalBuffer->width, normalBuffer->height);
        }

        // Set output buffer
        filter->setImage("output", colorBuffer.data.data(),
                          oidn::Format::Float3, colorBuffer.width, colorBuffer.height);

        // Set filter parameters
        filter->set("hdr", true);
        filter->set("srgb", false);
        filter->set("cleanAux", settings.cleanAuxiliary);

        // Commit and execute
        filter->commit();
        filter->execute();

        // Check for errors
        const char* errorMessage;
        if (device->getError(errorMessage) != oidn::Error::None) {
            m_stats.errorMessage = std::string(errorMessage);
            return false;
        }
        
        m_stats.memoryUsed = colorBuffer.getMemoryUsage();
        if (albedoBuffer) m_stats.memoryUsed += albedoBuffer->getMemoryUsage();
        if (normalBuffer) m_stats.memoryUsed += normalBuffer->getMemoryUsage();
        
        return true;
        
    } catch (const std::exception& e) {
        m_stats.errorMessage = e.what();
        return false;
    }
#else
    return false;
#endif
}

bool AIDenoiser::denoiseTiled(
    DenoisingBuffer& colorBuffer,
    const DenoisingBuffer* albedoBuffer,
    const DenoisingBuffer* normalBuffer,
    const DenoisingSettings& settings
) {
#ifdef PHOTON_USE_OIDN
    if (!m_initialized && !initialize(settings)) {
        return false;
    }
    
    try {
        int tileSize = calculateOptimalTileSize(colorBuffer.width, colorBuffer.height, settings);
        int tilesX = (colorBuffer.width + tileSize - 1) / tileSize;
        int tilesY = (colorBuffer.height + tileSize - 1) / tileSize;
        
        m_stats.tilesProcessed = 0;
        
        if (settings.verbose) {
            std::cout << "[AI Denoiser] Processing " << tilesX * tilesY 
                      << " tiles of size " << tileSize << "x" << tileSize << std::endl;
        }
        
        // Process each tile
        for (int ty = 0; ty < tilesY; ++ty) {
            for (int tx = 0; tx < tilesX; ++tx) {
                int tileX = tx * tileSize;
                int tileY = ty * tileSize;
                int tileW = std::min(tileSize, colorBuffer.width - tileX);
                int tileH = std::min(tileSize, colorBuffer.height - tileY);
                
                if (!processTile(colorBuffer, albedoBuffer, normalBuffer,
                               tileX, tileY, tileW, tileH, settings)) {
                    return false;
                }
                
                m_stats.tilesProcessed++;
                
                // Progress callback
                if (m_progressCallback) {
                    float progress = static_cast<float>(m_stats.tilesProcessed) / (tilesX * tilesY);
                    m_progressCallback(progress);
                }
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        m_stats.errorMessage = e.what();
        return false;
    }
#else
    return false;
#endif
}

std::vector<std::string> AIDenoiser::getSupportedDevices() {
    std::vector<std::string> devices;
#ifdef PHOTON_USE_OIDN
    devices.push_back("CPU");
    // Note: OIDN primarily uses CPU, but can leverage GPU acceleration
    // through Intel's oneAPI and SYCL in newer versions
#endif
    return devices;
}

size_t AIDenoiser::estimateMemoryUsage(int width, int height, const DenoisingSettings& settings) {
    size_t baseMemory = width * height * 3 * sizeof(float); // Color buffer
    
    if (settings.useAlbedo) {
        baseMemory += width * height * 3 * sizeof(float); // Albedo buffer
    }
    
    if (settings.useNormals) {
        baseMemory += width * height * 3 * sizeof(float); // Normal buffer
    }
    
    // OIDN internal memory overhead (estimated)
    baseMemory *= 2; // Roughly 2x for internal processing
    
    return baseMemory;
}

bool AIDenoiser::initializeDevice() {
#ifdef PHOTON_USE_OIDN
    try {
        oidn::DeviceRef device = oidn::newDevice();
        device.commit();

        // Check for errors
        const char* errorMessage;
        if (device.getError(errorMessage) != oidn::Error::None) {
            std::cerr << "[AI Denoiser ERROR] Device initialization failed: " << errorMessage << std::endl;
            return false;
        }

        // Store as raw pointer
        m_device = new oidn::DeviceRef(device);

        return true;

    } catch (const std::exception& e) {
        std::cerr << "[AI Denoiser ERROR] Device initialization exception: " << e.what() << std::endl;
        return false;
    }
#else
    return false;
#endif
}

bool AIDenoiser::createFilter(const DenoisingSettings& settings) {
#ifdef PHOTON_USE_OIDN
    try {
        // Create appropriate filter based on algorithm
        const char* filterType = (settings.algorithm == DenoisingAlgorithm::RTLightmap) ?
                                "RTLightmap" : "RT";

        oidn::DeviceRef* device = static_cast<oidn::DeviceRef*>(m_device);
        oidn::FilterRef filter = device->newFilter(filterType);

        // Check for errors
        const char* errorMessage;
        if (device->getError(errorMessage) != oidn::Error::None) {
            std::cerr << "[AI Denoiser ERROR] Filter creation failed: " << errorMessage << std::endl;
            return false;
        }

        // Store as raw pointer
        m_filter = new oidn::FilterRef(filter);

        return true;

    } catch (const std::exception& e) {
        std::cerr << "[AI Denoiser ERROR] Filter creation exception: " << e.what() << std::endl;
        return false;
    }
#else
    return false;
#endif
}

bool AIDenoiser::processTile(
    DenoisingBuffer& colorBuffer,
    const DenoisingBuffer* albedoBuffer,
    const DenoisingBuffer* normalBuffer,
    int tileX, int tileY, int tileWidth, int tileHeight,
    const DenoisingSettings& settings
) {
#ifdef PHOTON_USE_OIDN
    try {
        // Create tile buffers
        DenoisingBuffer tileColor(tileWidth, tileHeight, 3);

        // Extract tile from main buffer
        for (int y = 0; y < tileHeight; ++y) {
            for (int x = 0; x < tileWidth; ++x) {
                Color3 pixel = colorBuffer.getPixel(tileX + x, tileY + y);
                tileColor.setPixel(x, y, pixel);
            }
        }

        // Process tile (simplified - full implementation would handle auxiliary buffers)
        if (!denoise(tileColor, nullptr, nullptr, settings)) {
            return false;
        }

        // Copy back to main buffer
        for (int y = 0; y < tileHeight; ++y) {
            for (int x = 0; x < tileWidth; ++x) {
                Color3 pixel = tileColor.getPixel(x, y);
                colorBuffer.setPixel(tileX + x, tileY + y, pixel);
            }
        }

        return true;

    } catch (const std::exception& e) {
        m_stats.errorMessage = e.what();
        return false;
    }
#else
    return false;
#endif
}

void AIDenoiser::filmToBuffer(const Film& film, DenoisingBuffer& buffer) {
    for (int y = 0; y < buffer.height; ++y) {
        for (int x = 0; x < buffer.width; ++x) {
            Color3 pixel = film.getPixel(x, y);
            buffer.setPixel(x, y, pixel);
        }
    }
}

void AIDenoiser::bufferToFilm(const DenoisingBuffer& buffer, Film& film) {
    for (int y = 0; y < buffer.height; ++y) {
        for (int x = 0; x < buffer.width; ++x) {
            Color3 pixel = buffer.getPixel(x, y);
            film.setPixel(x, y, pixel);
        }
    }
}

int AIDenoiser::calculateOptimalTileSize(int width, int height, const DenoisingSettings& settings) {
    // Start with user-specified tile size
    int tileSize = settings.tileSize;

    // Adjust based on image size and quality
    if (settings.quality == DenoisingQuality::FAST) {
        tileSize = std::min(tileSize, 256);
    } else if (settings.quality == DenoisingQuality::ULTRA) {
        tileSize = std::max(tileSize, 512);
    }

    // Ensure tile size doesn't exceed image dimensions
    tileSize = std::min(tileSize, std::min(width, height));

    // Ensure minimum tile size
    tileSize = std::max(tileSize, 64);

    return tileSize;
}

bool AIDenoiser::validateBuffers(
    const DenoisingBuffer& colorBuffer,
    const DenoisingBuffer* albedoBuffer,
    const DenoisingBuffer* normalBuffer
) {
    if (colorBuffer.width <= 0 || colorBuffer.height <= 0 || colorBuffer.channels < 3) {
        return false;
    }

    if (albedoBuffer) {
        if (albedoBuffer->width != colorBuffer.width ||
            albedoBuffer->height != colorBuffer.height ||
            albedoBuffer->channels < 3) {
            return false;
        }
    }

    if (normalBuffer) {
        if (normalBuffer->width != colorBuffer.width ||
            normalBuffer->height != colorBuffer.height ||
            normalBuffer->channels < 3) {
            return false;
        }
    }

    return true;
}

// DenoisingManager implementation
DenoisingManager& DenoisingManager::getInstance() {
    static DenoisingManager instance;
    return instance;
}

bool DenoisingManager::initialize(const DenoisingSettings& settings) {
    if (m_initialized) {
        return true;
    }

    m_initialized = m_denoiser.initialize(settings);
    return m_initialized;
}

void DenoisingManager::shutdown() {
    if (m_initialized) {
        m_denoiser.shutdown();
        m_initialized = false;
    }
}

bool DenoisingManager::denoise(Film& film, const DenoisingSettings& settings) {
    if (!m_initialized && !initialize(settings)) {
        return false;
    }

    return m_denoiser.denoise(film, settings);
}

} // namespace photon
