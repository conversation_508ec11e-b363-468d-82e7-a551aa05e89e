# Intel OIDN Installation Guide

**Data**: 2025-06-21  
**Versione**: Intel OIDN 2.1.0  
**Status**: ✅ **INSTALLATO E CONFIGURATO**

## 📋 Overview

Questa guida documenta l'installazione e configurazione di **Intel Open Image Denoise (OIDN)** per PhotonRender. OIDN fornisce AI denoising di alta qualità per il rendering ray tracing.

## ✅ **STATO ATTUALE: INSTALLAZIONE COMPLETATA**

Intel OIDN è **già installato e configurato correttamente** nel sistema PhotonRender:

- ✅ **Intel OIDN 2.1.0**: Installato in `external/oidn/`
- ✅ **Headers**: `oidn.h`, `oidn.hpp` disponibili
- ✅ **Libraries**: `OpenImageDenoise.lib` configurato
- ✅ **DLLs**: Copiate automaticamente in output
- ✅ **CMake Integration**: FindOpenImageDenoise.cmake configurato
- ✅ **Build System**: CMake rileva OIDN automaticamente
- ✅ **Test Tools**: `oidnTest.exe` e `oidnDenoise.exe` funzionanti

## 🔧 Installazione (Per Riferimento)

### **Passo 1: Download**

1. **Sito ufficiale**: https://www.openimagedenoise.org/downloads.html
2. **Versione**: Intel Open Image Denoise v2.1.0
3. **Platform**: Windows (x64)
4. **File**: `oidn-2.1.0.x64.windows.zip`

### **Passo 2: Estrazione**

```
photon-render/
└── external/
    └── oidn/
        ├── bin/
        │   ├── OpenImageDenoise.dll
        │   ├── OpenImageDenoise_core.dll
        │   ├── oidnTest.exe
        │   ├── oidnDenoise.exe
        │   └── tbb12.dll
        ├── include/
        │   └── OpenImageDenoise/
        │       ├── oidn.h
        │       ├── oidn.hpp
        │       └── config.h
        └── lib/
            ├── OpenImageDenoise.lib
            └── OpenImageDenoise_core.lib
```

### **Passo 3: CMake Configuration**

Il file `cmake/FindOpenImageDenoise.cmake` è configurato per:

```cmake
# Set OIDN root path
set(OIDN_ROOT_PATH "${CMAKE_SOURCE_DIR}/external/oidn")

# Find include directory
find_path(OIDN_INCLUDE_DIR
    NAMES OpenImageDenoise/oidn.h
    PATHS ${OIDN_ROOT_PATH}/include
    NO_DEFAULT_PATH
)

# Find library
find_library(OIDN_LIBRARY
    NAMES OpenImageDenoise
    PATHS ${OIDN_ROOT_PATH}/lib
    NO_DEFAULT_PATH
)
```

### **Passo 4: Build Integration**

Nel `CMakeLists.txt`:

```cmake
# Intel OIDN support
if(USE_OIDN)
    find_package(OpenImageDenoise QUIET)
    
    if(OpenImageDenoise_FOUND)
        message(STATUS "Intel OIDN found and enabled")
        target_link_libraries(photon_core PUBLIC OpenImageDenoise)
        target_compile_definitions(photon_core PUBLIC PHOTON_USE_OIDN)
        
        # Copy OIDN DLLs to output directory
        if(WIN32)
            add_custom_command(TARGET photon_core POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${CMAKE_SOURCE_DIR}/external/oidn/bin/OpenImageDenoise.dll"
                $<TARGET_FILE_DIR:photon_core>
            )
        endif()
    endif()
endif()
```

## 🧪 Verifica Installazione

### **Test 1: OIDN Test Tool**

```bash
cd C:\xampp\htdocs\progetti\photon-render
external\oidn\bin\oidnTest.exe --help
```

**Output atteso**:
```
Intel(R) Open Image Denoise Test
Usage: oidnTest [options]
Options:
  --help                show this help message
  --device <name>       device type (cpu, cuda, hip, sycl)
  --libidentify         print library identification
```

### **Test 2: Library Identification**

```bash
external\oidn\bin\oidnTest.exe --libidentify
```

**Output atteso**:
```
Intel(R) Open Image Denoise 2.1.0
Library: OpenImageDenoise 2.1.0
Devices: CPU, CUDA, HIP, SYCL
```

### **Test 3: CMake Detection**

```bash
cmake -B build -S . -DUSE_OIDN=ON
```

**Output atteso**:
```
-- Found Intel OIDN:
--   Include dir: C:/xampp/htdocs/progetti/photon-render/external/oidn/include
--   Library: C:/xampp/htdocs/progetti/photon-render/external/oidn/lib/OpenImageDenoise.lib
--   DLL: C:/xampp/htdocs/progetti/photon-render/external/oidn/bin/OpenImageDenoise.dll
-- Intel OIDN found and enabled
-- OIDN support: ON
```

## 💻 Usage in PhotonRender

### **C++ Integration**

```cpp
#ifdef PHOTON_USE_OIDN
#include <OpenImageDenoise/oidn.hpp>

class PhotonDenoiser {
public:
    bool initialize() {
        device = oidn::newDevice();
        device.commit();
        
        filter = device.newFilter("RT");
        return true;
    }
    
    void denoise(float* color, float* albedo, float* normal, 
                 float* output, int width, int height) {
        filter.setImage("color", color, oidn::Format::Float3, width, height);
        filter.setImage("albedo", albedo, oidn::Format::Float3, width, height);
        filter.setImage("normal", normal, oidn::Format::Float3, width, height);
        filter.setImage("output", output, oidn::Format::Float3, width, height);
        filter.commit();
        filter.execute();
    }
    
private:
    oidn::DeviceRef device;
    oidn::FilterRef filter;
};
#endif
```

### **Renderer Integration**

```cpp
// In renderer.cpp
#ifdef PHOTON_USE_OIDN
    if (settings.enable_denoising) {
        denoiser.denoise(color_buffer, albedo_buffer, normal_buffer, 
                        output_buffer, width, height);
    }
#endif
```

## 🔧 Configuration Options

### **Device Types**
- **CPU**: Default, sempre disponibile
- **CUDA**: Richiede NVIDIA GPU con CUDA
- **HIP**: AMD GPU support
- **SYCL**: Intel GPU support

### **Filter Types**
- **RT**: Ray tracing denoiser (default)
- **RTLightmap**: Per lightmap denoising

### **Quality Settings**
```cpp
filter.set("hdr", true);           // HDR input
filter.set("srgb", false);        // Linear color space
filter.set("cleanAux", true);     // Clean auxiliary features
```

## 📊 Performance Characteristics

### **Memory Usage**
- **Overhead**: ~100MB per 1920x1080 image
- **Scaling**: Linear con resolution
- **Optimization**: Tile-based processing per large images

### **Performance**
- **CPU**: ~50ms per 1920x1080 (Intel i7)
- **GPU**: ~5ms per 1920x1080 (RTX 4070)
- **Quality**: Comparable to 10-50x more samples

### **Integration Overhead**
- **Setup**: One-time ~1ms initialization
- **Per-frame**: <0.1ms overhead
- **Memory**: Minimal additional allocation

## 🚨 Troubleshooting

### **Common Issues**

#### **1. DLL Not Found**
```
Error: OpenImageDenoise.dll not found
```
**Solution**: Verify DLLs are copied to output directory

#### **2. CMake Not Finding OIDN**
```
Warning: Intel OIDN requested but not found
```
**Solution**: Check `external/oidn/` directory structure

#### **3. Linking Errors**
```
Error: unresolved external symbol oidnNewDevice
```
**Solution**: Verify `OpenImageDenoise.lib` is linked

### **Debug Commands**

```bash
# Check DLL dependencies
dumpbin /dependents build/Release/photon_core.exe

# Verify OIDN installation
external\oidn\bin\oidnTest.exe --device cpu

# Test denoising
external\oidn\bin\oidnDenoise.exe --help
```

## 📋 File Checklist

### **Required Files**
- ✅ `external/oidn/include/OpenImageDenoise/oidn.h`
- ✅ `external/oidn/include/OpenImageDenoise/oidn.hpp`
- ✅ `external/oidn/lib/OpenImageDenoise.lib`
- ✅ `external/oidn/bin/OpenImageDenoise.dll`
- ✅ `external/oidn/bin/OpenImageDenoise_core.dll`
- ✅ `external/oidn/bin/tbb12.dll`
- ✅ `cmake/FindOpenImageDenoise.cmake`

### **Configuration Files**
- ✅ `CMakeLists.txt` (USE_OIDN=ON)
- ✅ `src/core/denoising/ai_denoiser.hpp`
- ✅ `src/core/denoising/ai_denoiser.cpp`

## 🎉 Success Confirmation

**Intel OIDN è installato e configurato correttamente quando:**

1. ✅ CMake trova OIDN: "Intel OIDN found and enabled"
2. ✅ Build compila senza errori OIDN
3. ✅ DLLs sono copiate in output directory
4. ✅ `oidnTest.exe` esegue senza errori
5. ✅ PhotonRender compila con `PHOTON_USE_OIDN` definito

**Status**: ✅ **TUTTI I CRITERI SODDISFATTI**

---

**Intel OIDN Installation Guide**  
*PhotonRender AI Denoising System*
