# PhotonRender - Local History Monitor
# Monitora l'uso della Local History di VS Code e fornisce statistiche
# Uso: .\scripts\monitor-local-history.ps1 [-ShowDetails] [-ShowLargestFiles]

param(
    [switch]$ShowDetails = $false,
    [switch]$ShowLargestFiles = $false
)

# Colori per output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"
$Magenta = "Magenta"

Write-Host "📊 PhotonRender - Local History Monitor" -ForegroundColor $Cyan
Write-Host "=======================================" -ForegroundColor $Cyan
Write-Host ""

# Funzione per formattare dimensioni
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size bytes"
    }
}

# Funzione per analizzare una cartella
function Analyze-HistoryFolder {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        return $null
    }
    
    $allFiles = Get-ChildItem $Path -Recurse -File -ErrorAction SilentlyContinue
    $totalSize = ($allFiles | Measure-Object -Property Length -Sum).Sum
    
    # Analisi per età
    $now = Get-Date
    $last7Days = $allFiles | Where-Object { $_.LastWriteTime -gt $now.AddDays(-7) }
    $last30Days = $allFiles | Where-Object { $_.LastWriteTime -gt $now.AddDays(-30) }
    $older30Days = $allFiles | Where-Object { $_.LastWriteTime -le $now.AddDays(-30) }
    
    # Analisi per tipo file
    $fileTypes = $allFiles | Group-Object Extension | Sort-Object Count -Descending
    
    # File più grandi
    $largestFiles = $allFiles | Sort-Object Length -Descending | Select-Object -First 10
    
    return @{
        TotalFiles = $allFiles.Count
        TotalSize = $totalSize
        Last7Days = @{
            Count = $last7Days.Count
            Size = ($last7Days | Measure-Object -Property Length -Sum).Sum
        }
        Last30Days = @{
            Count = $last30Days.Count
            Size = ($last30Days | Measure-Object -Property Length -Sum).Sum
        }
        Older30Days = @{
            Count = $older30Days.Count
            Size = ($older30Days | Measure-Object -Property Length -Sum).Sum
        }
        FileTypes = $fileTypes
        LargestFiles = $largestFiles
    }
}

# Percorsi Local History
$historyPaths = @()

# Windows
if ($env:APPDATA) {
    $historyPaths += @{
        Name = "VS Code"
        Path = "$env:APPDATA\Code\User\History"
    }
    $historyPaths += @{
        Name = "VS Code Insiders"
        Path = "$env:APPDATA\Code - Insiders\User\History"
    }
}

# Analizza ogni percorso
$totalStats = @{
    TotalFiles = 0
    TotalSize = 0
    TotalLast7Days = 0
    TotalLast30Days = 0
    TotalOlder30Days = 0
}

foreach ($historyPath in $historyPaths) {
    Write-Host "📁 Analizzando: $($historyPath.Name)" -ForegroundColor $Cyan
    Write-Host "   Percorso: $($historyPath.Path)" -ForegroundColor $Yellow
    
    $stats = Analyze-HistoryFolder -Path $historyPath.Path
    
    if ($stats -eq $null) {
        Write-Host "   ❌ Cartella non trovata" -ForegroundColor $Red
        Write-Host ""
        continue
    }
    
    # Aggiorna statistiche totali
    $totalStats.TotalFiles += $stats.TotalFiles
    $totalStats.TotalSize += $stats.TotalSize
    $totalStats.TotalLast7Days += $stats.Last7Days.Count
    $totalStats.TotalLast30Days += $stats.Last30Days.Count
    $totalStats.TotalOlder30Days += $stats.Older30Days.Count
    
    # Mostra statistiche base
    Write-Host "   📊 File totali: $($stats.TotalFiles)" -ForegroundColor $Green
    Write-Host "   💾 Dimensione totale: $(Format-FileSize $stats.TotalSize)" -ForegroundColor $Green
    Write-Host ""
    
    Write-Host "   📅 Distribuzione per età:" -ForegroundColor $Magenta
    Write-Host "      • Ultimi 7 giorni: $($stats.Last7Days.Count) file ($(Format-FileSize $stats.Last7Days.Size))" -ForegroundColor $Yellow
    Write-Host "      • Ultimi 30 giorni: $($stats.Last30Days.Count) file ($(Format-FileSize $stats.Last30Days.Size))" -ForegroundColor $Yellow
    Write-Host "      • Più di 30 giorni: $($stats.Older30Days.Count) file ($(Format-FileSize $stats.Older30Days.Size))" -ForegroundColor $Red
    
    if ($stats.Older30Days.Count -gt 0) {
        $potentialSavings = $stats.Older30Days.Size
        Write-Host "      💡 Spazio liberabile: $(Format-FileSize $potentialSavings)" -ForegroundColor $Green
    }
    
    Write-Host ""
    
    # Dettagli sui tipi di file
    if ($ShowDetails -and $stats.FileTypes.Count -gt 0) {
        Write-Host "   📋 Top 10 tipi di file:" -ForegroundColor $Magenta
        $stats.FileTypes | Select-Object -First 10 | ForEach-Object {
            $ext = if ($_.Name -eq "") { "(nessuna estensione)" } else { $_.Name }
            Write-Host "      - $ext : $($_.Count) file" -ForegroundColor $Yellow
        }
        Write-Host ""
    }
    
    # File più grandi
    if ($ShowLargestFiles -and $stats.LargestFiles.Count -gt 0) {
        Write-Host "   📈 Top 10 file più grandi:" -ForegroundColor $Magenta
        $stats.LargestFiles | ForEach-Object {
            $relativePath = $_.FullName.Replace($historyPath.Path, "...")
            Write-Host "      - $(Format-FileSize $_.Length) - $($_.Name)" -ForegroundColor $Yellow
        }
        Write-Host ""
    }
}

# Riepilogo finale
Write-Host "📊 RIEPILOGO TOTALE" -ForegroundColor $Cyan
Write-Host "===================" -ForegroundColor $Cyan
Write-Host "📄 File totali: $($totalStats.TotalFiles)" -ForegroundColor $Green
Write-Host "💾 Dimensione totale: $(Format-FileSize $totalStats.TotalSize)" -ForegroundColor $Green
Write-Host ""

Write-Host "📅 Distribuzione temporale:" -ForegroundColor $Magenta
Write-Host "   - Ultimi 7 giorni: $($totalStats.TotalLast7Days) file" -ForegroundColor $Yellow
Write-Host "   - Ultimi 30 giorni: $($totalStats.TotalLast30Days) file" -ForegroundColor $Yellow
Write-Host "   - Piu di 30 giorni: $($totalStats.TotalOlder30Days) file" -ForegroundColor $Red

if ($totalStats.TotalOlder30Days -gt 0) {
    Write-Host ""
    Write-Host "💡 RACCOMANDAZIONI:" -ForegroundColor $Cyan
    Write-Host "   - Hai $($totalStats.TotalOlder30Days) file piu vecchi di 30 giorni" -ForegroundColor $Yellow
    Write-Host "   - Esegui: .\scripts\cleanup-local-history.ps1 -DryRun per vedere cosa puo essere rimosso" -ForegroundColor $Yellow
    Write-Host "   - Esegui: .\scripts\cleanup-local-history.ps1 per pulire effettivamente" -ForegroundColor $Yellow
}

# Valutazione configurazione
Write-Host ""
Write-Host "⚙️  STATO CONFIGURAZIONE:" -ForegroundColor $Cyan

$settingsPath = ".vscode\settings.json"
if (Test-Path $settingsPath) {
    $settings = Get-Content $settingsPath -Raw | ConvertFrom-Json -ErrorAction SilentlyContinue
    
    if ($settings.settings."workbench.localHistory.maxFileEntries") {
        $maxEntries = $settings.settings."workbench.localHistory.maxFileEntries"
        Write-Host "   ✅ Local History limitata a $maxEntries versioni per file" -ForegroundColor $Green
    } else {
        Write-Host "   ⚠️  Local History non limitata (raccomandato: 3-5 versioni)" -ForegroundColor $Yellow
    }
    
    if ($settings.settings."workbench.localHistory.exclude") {
        Write-Host "   ✅ Esclusioni configurate per file build/temporanei" -ForegroundColor $Green
    } else {
        Write-Host "   ⚠️  Nessuna esclusione configurata (raccomandato: escludi build/)" -ForegroundColor $Yellow
    }
} else {
    Write-Host "   ❌ File .vscode/settings.json non trovato" -ForegroundColor $Red
    Write-Host "   💡 Esegui la configurazione ottimizzata per PhotonRender" -ForegroundColor $Yellow
}

Write-Host ""
Write-Host "🎯 COMANDI UTILI:" -ForegroundColor $Cyan
Write-Host "   - Monitor dettagliato: .\scripts\monitor-local-history.ps1 -ShowDetails -ShowLargestFiles" -ForegroundColor $Yellow
Write-Host "   - Pulizia test: .\scripts\cleanup-local-history.ps1 -DryRun" -ForegroundColor $Yellow
Write-Host "   - Pulizia effettiva: .\scripts\cleanup-local-history.ps1" -ForegroundColor $Yellow

Write-Host ""
Write-Host "✅ Monitoraggio Local History completato!" -ForegroundColor $Green
