// src/core/texture/texture_lod.cpp
// PhotonRender - Level of Detail (LOD) System Implementation
// Automatic LOD generation and management for texture optimization

#include "texture_lod.hpp"
#include <algorithm>
#include <cmath>
#include <iostream>

namespace photon {

LODTexture::LODTexture() : ImageTexture() {
}

LODTexture::LODTexture(int width, int height, int channels, const float* data)
    : ImageTexture(width, height, channels, data) {
}

bool LODTexture::generateMipmaps(const LODSettings& settings) {
    const auto& data = getData();
    int width = getTextureWidth();
    int height = getTextureHeight();

    if (data.empty() || width <= 0 || height <= 0) {
        return false;
    }

    m_lodSettings = settings;
    m_lodLevels.clear();

    // Create level 0 (original resolution)
    LODLevel level0(0, width, height);
    level0.data = data;
    level0.memoryUsage = data.size() * sizeof(float);
    m_lodLevels.push_back(std::move(level0));

    if (!settings.generateMipmaps) {
        return true;
    }

    // Generate mipmap chain
    int maxLevels = std::min(settings.maxLevels, calculateMaxLevels(width, height));
    
    for (int level = 1; level < maxLevels; ++level) {
        const LODLevel& sourceLevel = m_lodLevels[level - 1];
        
        // Stop if source level is too small
        if (sourceLevel.width <= 1 && sourceLevel.height <= 1) {
            break;
        }
        
        LODLevel newLevel = generateMipmapLevel(sourceLevel, settings.filter);
        if (newLevel.data.empty()) {
            break;
        }
        
        m_lodLevels.push_back(std::move(newLevel));
    }
    
    return !m_lodLevels.empty();
}

Color3 LODTexture::sampleLOD(const Vec2& uv, float distance, float screenSize) const {
    if (m_lodLevels.empty()) {
        return Color3(1.0f, 0.0f, 1.0f); // Magenta for error
    }
    
    float lodLevel = calculateLOD(distance, screenSize);
    return sampleAtLOD(uv, lodLevel);
}

Color3 LODTexture::sampleAtLOD(const Vec2& uv, float lodLevel) const {
    if (m_lodLevels.empty()) {
        return Color3(1.0f, 0.0f, 1.0f);
    }
    
    // Apply LOD bias
    lodLevel += m_lodBias + LODManager::getInstance().getGlobalLODBias();
    
    // Clamp LOD level
    lodLevel = std::clamp(lodLevel, 0.0f, static_cast<float>(m_lodLevels.size() - 1));
    
    int level0 = static_cast<int>(std::floor(lodLevel));
    int level1 = std::min(level0 + 1, static_cast<int>(m_lodLevels.size() - 1));
    
    if (level0 == level1 || !m_trilinearFiltering) {
        // No interpolation needed or trilinear disabled
        return sampleFromLevel(m_lodLevels[level0], uv);
    }
    
    // Trilinear interpolation between levels
    float t = lodLevel - level0;
    return interpolateLevels(m_lodLevels[level0], m_lodLevels[level1], uv, t);
}

float LODTexture::calculateLOD(float distance, float screenSize) const {
    if (m_lodLevels.empty()) {
        return 0.0f;
    }
    
    switch (m_selectionMethod) {
        case LODSelectionMethod::DISTANCE: {
            // Simple distance-based LOD
            float lodScale = LODManager::getInstance().getLODDistanceScale();
            return std::log2(distance * lodScale + 1.0f);
        }
        
        case LODSelectionMethod::SCREEN_SIZE: {
            // Screen space size-based LOD
            if (screenSize <= 0.0f) {
                return static_cast<float>(m_lodLevels.size() - 1);
            }
            
            float texelSize = 1.0f / std::max(m_width, m_height);
            float lodLevel = std::log2(texelSize / screenSize);
            return std::max(0.0f, lodLevel);
        }
        
        case LODSelectionMethod::MANUAL:
            // Manual LOD (use bias as level)
            return std::abs(m_lodBias);
        
        case LODSelectionMethod::ADAPTIVE:
            // Adaptive based on distance and screen size
            float distanceLOD = std::log2(distance + 1.0f);
            float sizeLOD = screenSize > 0.0f ? std::log2(1.0f / screenSize) : 0.0f;
            return (distanceLOD + sizeLOD) * 0.5f;
        
        default:
            return 0.0f;
    }
}

const LODLevel* LODTexture::getLODLevel(int level) const {
    if (level < 0 || level >= static_cast<int>(m_lodLevels.size())) {
        return nullptr;
    }
    return &m_lodLevels[level];
}

size_t LODTexture::getTotalMemoryUsage() const {
    size_t total = 0;
    for (const auto& level : m_lodLevels) {
        total += level.memoryUsage;
    }
    return total;
}

LODLevel LODTexture::generateMipmapLevel(const LODLevel& sourceLevel, MipmapFilter filter) const {
    int newWidth = std::max(1, sourceLevel.width / 2);
    int newHeight = std::max(1, sourceLevel.height / 2);
    
    LODLevel newLevel(sourceLevel.level + 1, newWidth, newHeight);
    
    switch (filter) {
        case MipmapFilter::BOX:
            newLevel.data = applyBoxFilter(sourceLevel.data, sourceLevel.width, sourceLevel.height, m_channels);
            break;
        case MipmapFilter::BILINEAR:
            newLevel.data = applyBilinearFilter(sourceLevel.data, sourceLevel.width, sourceLevel.height, m_channels);
            break;
        case MipmapFilter::BICUBIC:
            newLevel.data = applyBicubicFilter(sourceLevel.data, sourceLevel.width, sourceLevel.height, m_channels);
            break;
        case MipmapFilter::LANCZOS:
            // For now, use bicubic as approximation
            newLevel.data = applyBicubicFilter(sourceLevel.data, sourceLevel.width, sourceLevel.height, m_channels);
            break;
    }
    
    newLevel.memoryUsage = newLevel.data.size() * sizeof(float);
    return newLevel;
}

std::vector<float> LODTexture::applyBoxFilter(const std::vector<float>& source, 
                                             int srcWidth, int srcHeight, int channels) const {
    int dstWidth = std::max(1, srcWidth / 2);
    int dstHeight = std::max(1, srcHeight / 2);
    
    std::vector<float> result(dstWidth * dstHeight * channels, 0.0f);
    
    for (int y = 0; y < dstHeight; ++y) {
        for (int x = 0; x < dstWidth; ++x) {
            // Sample 2x2 block from source
            int srcX = x * 2;
            int srcY = y * 2;
            
            for (int c = 0; c < channels; ++c) {
                float sum = 0.0f;
                int count = 0;
                
                for (int dy = 0; dy < 2; ++dy) {
                    for (int dx = 0; dx < 2; ++dx) {
                        int sx = std::min(srcX + dx, srcWidth - 1);
                        int sy = std::min(srcY + dy, srcHeight - 1);
                        
                        int srcIndex = (sy * srcWidth + sx) * channels + c;
                        sum += source[srcIndex];
                        count++;
                    }
                }
                
                int dstIndex = (y * dstWidth + x) * channels + c;
                result[dstIndex] = sum / count;
            }
        }
    }
    
    return result;
}

std::vector<float> LODTexture::applyBilinearFilter(const std::vector<float>& source, 
                                                  int srcWidth, int srcHeight, int channels) const {
    int dstWidth = std::max(1, srcWidth / 2);
    int dstHeight = std::max(1, srcHeight / 2);
    
    std::vector<float> result(dstWidth * dstHeight * channels, 0.0f);
    
    float scaleX = static_cast<float>(srcWidth) / dstWidth;
    float scaleY = static_cast<float>(srcHeight) / dstHeight;
    
    for (int y = 0; y < dstHeight; ++y) {
        for (int x = 0; x < dstWidth; ++x) {
            float srcX = (x + 0.5f) * scaleX - 0.5f;
            float srcY = (y + 0.5f) * scaleY - 0.5f;
            
            int x0 = static_cast<int>(std::floor(srcX));
            int y0 = static_cast<int>(std::floor(srcY));
            int x1 = std::min(x0 + 1, srcWidth - 1);
            int y1 = std::min(y0 + 1, srcHeight - 1);
            
            x0 = std::max(x0, 0);
            y0 = std::max(y0, 0);
            
            float fx = srcX - x0;
            float fy = srcY - y0;
            
            for (int c = 0; c < channels; ++c) {
                float p00 = source[(y0 * srcWidth + x0) * channels + c];
                float p10 = source[(y0 * srcWidth + x1) * channels + c];
                float p01 = source[(y1 * srcWidth + x0) * channels + c];
                float p11 = source[(y1 * srcWidth + x1) * channels + c];
                
                float p0 = p00 * (1.0f - fx) + p10 * fx;
                float p1 = p01 * (1.0f - fx) + p11 * fx;
                float p = p0 * (1.0f - fy) + p1 * fy;
                
                int dstIndex = (y * dstWidth + x) * channels + c;
                result[dstIndex] = p;
            }
        }
    }
    
    return result;
}

std::vector<float> LODTexture::applyBicubicFilter(const std::vector<float>& source, 
                                                 int srcWidth, int srcHeight, int channels) const {
    int dstWidth = std::max(1, srcWidth / 2);
    int dstHeight = std::max(1, srcHeight / 2);
    
    std::vector<float> result(dstWidth * dstHeight * channels, 0.0f);
    
    float scaleX = static_cast<float>(srcWidth) / dstWidth;
    float scaleY = static_cast<float>(srcHeight) / dstHeight;
    
    for (int y = 0; y < dstHeight; ++y) {
        for (int x = 0; x < dstWidth; ++x) {
            float srcX = (x + 0.5f) * scaleX - 0.5f;
            float srcY = (y + 0.5f) * scaleY - 0.5f;
            
            int x1 = static_cast<int>(std::floor(srcX));
            int y1 = static_cast<int>(std::floor(srcY));
            
            float fx = srcX - x1;
            float fy = srcY - y1;
            
            for (int c = 0; c < channels; ++c) {
                float values[4];
                
                for (int j = 0; j < 4; ++j) {
                    int sy = std::clamp(y1 - 1 + j, 0, srcHeight - 1);
                    
                    float row[4];
                    for (int i = 0; i < 4; ++i) {
                        int sx = std::clamp(x1 - 1 + i, 0, srcWidth - 1);
                        row[i] = source[(sy * srcWidth + sx) * channels + c];
                    }
                    
                    values[j] = cubicInterpolate(row[0], row[1], row[2], row[3], fx);
                }
                
                float finalValue = cubicInterpolate(values[0], values[1], values[2], values[3], fy);
                
                int dstIndex = (y * dstWidth + x) * channels + c;
                result[dstIndex] = std::clamp(finalValue, 0.0f, 1.0f);
            }
        }
    }
    
    return result;
}

Color3 LODTexture::sampleFromLevel(const LODLevel& level, const Vec2& uv) const {
    Vec2 clampedUV = clampUV(uv);
    
    float x = clampedUV.x * (level.width - 1);
    float y = clampedUV.y * (level.height - 1);
    
    int x0 = static_cast<int>(std::floor(x));
    int y0 = static_cast<int>(std::floor(y));
    int x1 = std::min(x0 + 1, level.width - 1);
    int y1 = std::min(y0 + 1, level.height - 1);
    
    float fx = x - x0;
    float fy = y - y0;
    
    // Bilinear interpolation
    auto getPixel = [&](int px, int py) -> Color3 {
        int index = (py * level.width + px) * m_channels;
        if (m_channels >= 3) {
            return Color3(level.data[index], level.data[index + 1], level.data[index + 2]);
        } else {
            float val = level.data[index];
            return Color3(val, val, val);
        }
    };
    
    Color3 p00 = getPixel(x0, y0);
    Color3 p10 = getPixel(x1, y0);
    Color3 p01 = getPixel(x0, y1);
    Color3 p11 = getPixel(x1, y1);
    
    Color3 p0 = p00 * (1.0f - fx) + p10 * fx;
    Color3 p1 = p01 * (1.0f - fx) + p11 * fx;
    
    return p0 * (1.0f - fy) + p1 * fy;
}

Color3 LODTexture::interpolateLevels(const LODLevel& level0, const LODLevel& level1, 
                                    const Vec2& uv, float t) const {
    Color3 color0 = sampleFromLevel(level0, uv);
    Color3 color1 = sampleFromLevel(level1, uv);
    
    return color0 * (1.0f - t) + color1 * t;
}

int LODTexture::calculateMaxLevels(int width, int height) const {
    int maxDim = std::max(width, height);
    return static_cast<int>(std::floor(std::log2(maxDim))) + 1;
}

float LODTexture::cubicInterpolate(float p0, float p1, float p2, float p3, float t) const {
    float a = -0.5f * p0 + 1.5f * p1 - 1.5f * p2 + 0.5f * p3;
    float b = p0 - 2.5f * p1 + 2.0f * p2 - 0.5f * p3;
    float c = -0.5f * p0 + 0.5f * p2;
    float d = p1;
    
    return a * t * t * t + b * t * t + c * t + d;
}

Vec2 LODTexture::clampUV(const Vec2& uv) const {
    return Vec2(std::clamp(uv.x, 0.0f, 1.0f), std::clamp(uv.y, 0.0f, 1.0f));
}

// LODManager implementation
LODManager& LODManager::getInstance() {
    static LODManager instance;
    return instance;
}

float LODManager::calculateScreenSize(float worldSize, float distance, float fov, int screenHeight) const {
    if (distance <= 0.0f || fov <= 0.0f || screenHeight <= 0) {
        return 1.0f;
    }
    
    // Calculate screen space size
    float tanHalfFOV = std::tan(fov * 0.5f);
    float screenSpaceSize = (worldSize * screenHeight) / (2.0f * distance * tanHalfFOV);
    
    return std::max(0.001f, screenSpaceSize);
}

} // namespace photon
