// src/qa/quality_assurance_system.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Quality Assurance System Implementation

#include "quality_assurance_system.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <thread>
#include <future>
#include <iomanip>

namespace photon {
namespace qa {

// TestResult Implementation
std::string TestResult::getStatusString() const {
    switch (status) {
        case TestStatus::PASSED: return "PASSED";
        case TestStatus::FAILED: return "FAILED";
        case TestStatus::SKIPPED: return "SKIPPED";
        case TestStatus::TIMEOUT: return "TIMEOUT";
        case TestStatus::ERROR: return "ERROR";
        case TestStatus::NOT_RUN: return "NOT_RUN";
        default: return "UNKNOWN";
    }
}

std::string TestResult::getSeverityString() const {
    switch (severity) {
        case TestSeverity::CRITICAL: return "CRITICAL";
        case TestSeverity::HIGH: return "HIGH";
        case TestSeverity::MEDIUM: return "MEDIUM";
        case TestSeverity::LOW: return "LOW";
        case TestSeverity::INFORMATIONAL: return "INFO";
        default: return "UNKNOWN";
    }
}

std::string TestResult::getCategoryString() const {
    switch (category) {
        case TestCategory::UNIT: return "UNIT";
        case TestCategory::INTEGRATION: return "INTEGRATION";
        case TestCategory::PERFORMANCE: return "PERFORMANCE";
        case TestCategory::REGRESSION: return "REGRESSION";
        case TestCategory::MEMORY: return "MEMORY";
        case TestCategory::GPU: return "GPU";
        case TestCategory::STRESS: return "STRESS";
        case TestCategory::VALIDATION: return "VALIDATION";
        case TestCategory::COMPATIBILITY: return "COMPATIBILITY";
        default: return "UNKNOWN";
    }
}

// TestContext Implementation
TestContext::TestContext(const std::string& name, TestCategory cat)
    : test_name(name), category(cat), start_time(std::chrono::steady_clock::now()) {
    
    // Initialize memory monitoring
    initial_memory_usage = AdvancedMemoryManager::getInstance().getStatistics().current_usage;
    
    // Initialize profiler
    profiler = std::make_unique<PerformanceProfiler>();
    profiler->initialize();
}

TestContext::~TestContext() {
    if (profiler) {
        profiler->shutdown();
    }
}

void TestContext::startProfiling() {
    if (profiler) {
        profiler->beginFrame();
    }
}

void TestContext::stopProfiling() {
    if (profiler) {
        profiler->endFrame();
    }
}

TestResult TestContext::finalize(TestStatus status, const std::string& error) {
    auto end_time = std::chrono::steady_clock::now();
    auto execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    TestResult result(test_name, "", category, TestSeverity::HIGH);
    result.status = status;
    result.execution_time = execution_time;
    result.error_message = error;
    result.output_log = output_stream.str();
    
    // Calculate performance metrics
    if (profiler) {
        result.performance_score = profiler->getFPS() > 0 ? std::min(1.0, profiler->getFPS() / 60.0) : 1.0;
    }
    
    // Calculate memory usage
    auto current_memory = AdvancedMemoryManager::getInstance().getStatistics().current_usage;
    result.memory_usage_bytes = current_memory - initial_memory_usage;
    
    return result;
}

// QualityAssuranceSystem Implementation
QualityAssuranceSystem::QualityAssuranceSystem() : m_initialized(false) {}

QualityAssuranceSystem::~QualityAssuranceSystem() {
    shutdown();
}

bool QualityAssuranceSystem::initialize(const TestSuiteConfig& config) {
    if (m_initialized) return true;
    
    m_config = config;
    
    // Create output directory
    createOutputDirectory();
    
    // Initialize built-in tests
    registerBuiltinTests();
    
    m_initialized = true;
    return true;
}

void QualityAssuranceSystem::shutdown() {
    if (!m_initialized) return;
    
    m_registered_tests.clear();
    m_test_results.clear();
    m_custom_validators.clear();
    
    m_initialized = false;
}

void QualityAssuranceSystem::registerTest(const std::string& name, const std::string& description,
                                         TestCategory category, TestSeverity severity,
                                         std::function<bool(TestContext&)> test_function) {
    RegisteredTest test;
    test.name = name;
    test.description = description;
    test.category = category;
    test.severity = severity;
    test.test_function = test_function;
    
    m_registered_tests.push_back(test);
}

bool QualityAssuranceSystem::runAllTests() {
    if (!m_initialized) return false;
    
    std::cout << "=== PhotonRender Quality Assurance System ===" << std::endl;
    std::cout << "Running " << m_registered_tests.size() << " tests..." << std::endl;
    
    m_test_results.clear();
    m_test_results.reserve(m_registered_tests.size());
    
    auto start_time = std::chrono::steady_clock::now();
    
    // Filter tests based on configuration
    std::vector<RegisteredTest> tests_to_run;
    for (const auto& test : m_registered_tests) {
        bool should_run = false;
        
        switch (test.category) {
            case TestCategory::UNIT: should_run = m_config.run_unit_tests; break;
            case TestCategory::INTEGRATION: should_run = m_config.run_integration_tests; break;
            case TestCategory::PERFORMANCE: should_run = m_config.run_performance_tests; break;
            case TestCategory::REGRESSION: should_run = m_config.run_regression_tests; break;
            case TestCategory::MEMORY: should_run = m_config.run_memory_tests; break;
            case TestCategory::GPU: should_run = m_config.run_gpu_tests; break;
            case TestCategory::STRESS: should_run = m_config.run_stress_tests; break;
            case TestCategory::VALIDATION: should_run = m_config.run_validation_tests; break;
            case TestCategory::COMPATIBILITY: should_run = m_config.run_compatibility_tests; break;
        }
        
        if (should_run) {
            tests_to_run.push_back(test);
        }
    }
    
    std::cout << "Filtered to " << tests_to_run.size() << " tests based on configuration." << std::endl;
    
    // Run tests (potentially in parallel)
    if (m_config.max_parallel_tests > 1) {
        // Parallel execution
        std::vector<std::future<TestResult>> futures;
        
        for (const auto& test : tests_to_run) {
            if (futures.size() >= static_cast<size_t>(m_config.max_parallel_tests)) {
                // Wait for one to complete
                for (auto it = futures.begin(); it != futures.end(); ++it) {
                    if (it->wait_for(std::chrono::milliseconds(10)) == std::future_status::ready) {
                        m_test_results.push_back(it->get());
                        futures.erase(it);
                        break;
                    }
                }
            }
            
            futures.push_back(std::async(std::launch::async, [this, test]() {
                return executeTest(test);
            }));
        }
        
        // Wait for remaining tests
        for (auto& future : futures) {
            m_test_results.push_back(future.get());
        }
    } else {
        // Sequential execution
        for (const auto& test : tests_to_run) {
            TestResult result = executeTest(test);
            m_test_results.push_back(result);
            
            // Stop on first failure if configured
            if (m_config.stop_on_first_failure && !result.isPassed()) {
                std::cout << "Stopping on first failure: " << test.name << std::endl;
                break;
            }
        }
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Generate summary
    TestSummary summary = getTestSummary();
    summary.total_execution_time = total_time;
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Total tests: " << summary.total_tests << std::endl;
    std::cout << "Passed: " << summary.passed_tests << std::endl;
    std::cout << "Failed: " << summary.failed_tests << std::endl;
    std::cout << "Skipped: " << summary.skipped_tests << std::endl;
    std::cout << "Critical failures: " << summary.critical_failures << std::endl;
    std::cout << "Overall score: " << std::fixed << std::setprecision(1) << (summary.overall_score * 100.0) << "%" << std::endl;
    std::cout << "Execution time: " << total_time.count() << "ms" << std::endl;
    std::cout << "All critical tests passed: " << (summary.all_critical_passed ? "YES" : "NO") << std::endl;
    
    // Generate reports if configured
    if (m_config.generate_detailed_reports) {
        generateReports();
    }
    
    return summary.all_critical_passed && summary.failed_tests == 0;
}

TestResult QualityAssuranceSystem::executeTest(const RegisteredTest& test) {
    std::cout << "Running " << test.name << "... ";
    std::cout.flush();
    
    TestContext context(test.name, test.category);
    context.startProfiling();
    
    bool test_passed = false;
    std::string error_message;
    
    try {
        // Set timeout
        auto future = std::async(std::launch::async, [&]() {
            return test.test_function(context);
        });
        
        if (future.wait_for(m_config.test_timeout) == std::future_status::timeout) {
            error_message = "Test timed out after " + std::to_string(m_config.test_timeout.count()) + " seconds";
            context.stopProfiling();
            TestResult result = context.finalize(TestStatus::TIMEOUT, error_message);
            result.test_description = test.description;
            result.severity = test.severity;
            std::cout << "TIMEOUT" << std::endl;
            return result;
        }
        
        test_passed = future.get();
        
    } catch (const std::exception& e) {
        error_message = "Exception: " + std::string(e.what());
        test_passed = false;
    } catch (...) {
        error_message = "Unknown exception occurred";
        test_passed = false;
    }
    
    context.stopProfiling();
    
    TestStatus status = test_passed ? TestStatus::PASSED : TestStatus::FAILED;
    if (!error_message.empty() && status != TestStatus::TIMEOUT) {
        status = TestStatus::ERROR;
    }
    
    TestResult result = context.finalize(status, error_message);
    result.test_description = test.description;
    result.severity = test.severity;
    
    // Validate result
    if (test_passed) {
        test_passed = validatePerformance(result) && 
                     validateMemoryUsage(result) && 
                     validateOutput(result);
        
        if (!test_passed) {
            result.status = TestStatus::FAILED;
            if (result.error_message.empty()) {
                result.error_message = "Validation failed";
            }
        }
    }
    
    // Check regression baseline
    if (test_passed && !m_regression_baselines.empty()) {
        if (!checkRegressionBaseline(result)) {
            result.status = TestStatus::FAILED;
            result.error_message += " (Regression detected)";
            test_passed = false;
        }
    }
    
    std::cout << result.getStatusString() << std::endl;
    
    // Save test artifacts if configured
    if (m_config.save_test_artifacts) {
        saveTestArtifacts(result);
    }
    
    return result;
}

QualityAssuranceSystem::TestSummary QualityAssuranceSystem::getTestSummary() const {
    TestSummary summary;
    summary.total_tests = static_cast<int>(m_test_results.size());
    summary.passed_tests = 0;
    summary.failed_tests = 0;
    summary.skipped_tests = 0;
    summary.critical_failures = 0;
    summary.overall_score = 0.0;
    summary.total_execution_time = std::chrono::milliseconds(0);
    summary.all_critical_passed = true;
    
    if (summary.total_tests == 0) return summary;
    
    double total_score = 0.0;
    
    for (const auto& result : m_test_results) {
        switch (result.status) {
            case TestStatus::PASSED:
                summary.passed_tests++;
                total_score += 1.0;
                break;
            case TestStatus::FAILED:
            case TestStatus::ERROR:
                summary.failed_tests++;
                if (result.isCritical()) {
                    summary.critical_failures++;
                    summary.all_critical_passed = false;
                }
                break;
            case TestStatus::SKIPPED:
                summary.skipped_tests++;
                total_score += 0.5; // Partial credit for skipped tests
                break;
            case TestStatus::TIMEOUT:
                summary.failed_tests++;
                if (result.isCritical()) {
                    summary.critical_failures++;
                    summary.all_critical_passed = false;
                }
                break;
            default:
                break;
        }
        
        summary.total_execution_time += result.execution_time;
    }
    
    summary.overall_score = total_score / summary.total_tests;
    
    return summary;
}

bool QualityAssuranceSystem::validatePerformance(const TestResult& result) {
    return result.performance_score >= m_config.min_performance_score;
}

bool QualityAssuranceSystem::validateMemoryUsage(const TestResult& result) {
    size_t max_bytes = m_config.max_memory_usage_mb * 1024 * 1024;
    return result.memory_usage_bytes <= max_bytes;
}

bool QualityAssuranceSystem::validateOutput(const TestResult& result) {
    auto it = m_custom_validators.find(result.test_name);
    if (it != m_custom_validators.end()) {
        return it->second(result.output_log);
    }
    return result.output_valid;
}

void QualityAssuranceSystem::createOutputDirectory() {
    try {
        std::filesystem::create_directories(m_config.output_directory);
    } catch (const std::exception& e) {
        std::cerr << "Failed to create output directory: " << e.what() << std::endl;
    }
}

void QualityAssuranceSystem::saveTestArtifacts(const TestResult& result) {
    try {
        std::string test_dir = m_config.output_directory + "/" + result.test_name;
        std::filesystem::create_directories(test_dir);
        
        // Save output log
        std::ofstream log_file(test_dir + "/output.log");
        log_file << result.output_log;
        log_file.close();
        
        // Save error log if there's an error
        if (!result.error_message.empty()) {
            std::ofstream error_file(test_dir + "/error.log");
            error_file << result.error_message;
            error_file.close();
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to save test artifacts for " << result.test_name << ": " << e.what() << std::endl;
    }
}

} // namespace qa
} // namespace photon
