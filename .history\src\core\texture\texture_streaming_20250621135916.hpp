// src/core/texture/texture_streaming.hpp
// PhotonRender - Texture Streaming System
// Advanced texture loading and management for large scenes

#pragma once

#include "texture.hpp"
#include "../math/vec3.hpp"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <thread>
#include <atomic>
#include <functional>

namespace photon {

/**
 * @brief Texture loading priority levels
 */
enum class TexturePriority {
    CRITICAL = 0,   ///< Must be loaded immediately (UI, HUD)
    HIGH = 1,       ///< High priority (nearby objects)
    MEDIUM = 2,     ///< Medium priority (visible objects)
    LOW = 3,        ///< Low priority (distant objects)
    BACKGROUND = 4  ///< Background loading (preload)
};

/**
 * @brief Texture loading state
 */
enum class TextureLoadState {
    UNLOADED,       ///< Not loaded
    LOADING,        ///< Currently loading
    LOADED,         ///< Fully loaded
    COMPRESSED,     ///< Loaded and compressed
    ERROR           ///< Loading failed
};

/**
 * @brief Texture streaming parameters
 */
struct StreamingParams {
    size_t maxMemoryMB = 512;           ///< Maximum texture memory in MB
    size_t maxCacheSize = 1000;         ///< Maximum number of cached textures
    float unloadDistance = 100.0f;      ///< Distance to unload textures
    float preloadDistance = 50.0f;      ///< Distance to preload textures
    int maxLoadingThreads = 4;          ///< Maximum loading threads
    bool enableCompression = true;      ///< Enable automatic compression
    bool enablePreloading = true;       ///< Enable background preloading
    
    static StreamingParams defaultParams() {
        return StreamingParams{512, 1000, 100.0f, 50.0f, 4, true, true};
    }
};

/**
 * @brief Texture request information
 */
struct TextureRequest {
    std::string path;                   ///< Texture file path
    TexturePriority priority;           ///< Loading priority
    Vec3 worldPosition;                 ///< World position for distance calculation
    float importance = 1.0f;            ///< Importance factor [0,1]
    std::function<void(std::shared_ptr<ImageTexture>)> callback; ///< Completion callback
    
    TextureRequest(const std::string& p, TexturePriority prio, const Vec3& pos = Vec3(0.0f))
        : path(p), priority(prio), worldPosition(pos) {}
};

/**
 * @brief Priority comparison for texture requests
 */
struct TextureRequestComparator {
    bool operator()(const TextureRequest& a, const TextureRequest& b) const {
        // Higher priority (lower number) comes first
        if (a.priority != b.priority) {
            return static_cast<int>(a.priority) > static_cast<int>(b.priority);
        }
        // If same priority, higher importance comes first
        return a.importance < b.importance;
    }
};

/**
 * @brief Texture cache entry
 */
struct TextureCacheEntry {
    std::shared_ptr<ImageTexture> texture;
    TextureLoadState state;
    float lastAccessTime;
    float loadTime;
    size_t memoryUsage;
    int accessCount;
    TexturePriority priority;
    
    TextureCacheEntry() 
        : state(TextureLoadState::UNLOADED), lastAccessTime(0.0f), 
          loadTime(0.0f), memoryUsage(0), accessCount(0), 
          priority(TexturePriority::MEDIUM) {}
};

/**
 * @brief Texture streaming manager
 */
class TextureStreamingManager {
public:
    /**
     * @brief Constructor
     * @param params Streaming parameters
     */
    explicit TextureStreamingManager(const StreamingParams& params = StreamingParams::defaultParams());
    
    /**
     * @brief Destructor
     */
    ~TextureStreamingManager();
    
    /**
     * @brief Initialize streaming system
     */
    bool initialize();
    
    /**
     * @brief Shutdown streaming system
     */
    void shutdown();
    
    /**
     * @brief Request texture loading
     * @param request Texture request
     * @return Texture if immediately available, nullptr if loading
     */
    std::shared_ptr<ImageTexture> requestTexture(const TextureRequest& request);
    
    /**
     * @brief Get texture if loaded
     * @param path Texture path
     * @return Texture if loaded, nullptr otherwise
     */
    std::shared_ptr<ImageTexture> getTexture(const std::string& path);
    
    /**
     * @brief Update streaming system (call per frame)
     * @param cameraPosition Current camera position
     * @param deltaTime Time since last update
     */
    void update(const Vec3& cameraPosition, float deltaTime);
    
    /**
     * @brief Preload textures in area
     * @param center Center position
     * @param radius Preload radius
     */
    void preloadArea(const Vec3& center, float radius);
    
    /**
     * @brief Unload distant textures
     * @param cameraPosition Current camera position
     */
    void unloadDistantTextures(const Vec3& cameraPosition);
    
    /**
     * @brief Force unload texture
     * @param path Texture path
     */
    void unloadTexture(const std::string& path);
    
    /**
     * @brief Clear all cached textures
     */
    void clearCache();
    
    /**
     * @brief Get memory usage statistics
     */
    struct MemoryStats {
        size_t totalMemoryMB;
        size_t usedMemoryMB;
        size_t cachedTextures;
        size_t loadingTextures;
        float cacheHitRate;
    };
    
    MemoryStats getMemoryStats() const;
    
    /**
     * @brief Set streaming parameters
     */
    void setStreamingParams(const StreamingParams& params);
    
    /**
     * @brief Get streaming parameters
     */
    const StreamingParams& getStreamingParams() const { return m_params; }

private:
    StreamingParams m_params;
    
    // Cache management
    std::unordered_map<std::string, TextureCacheEntry> m_textureCache;
    mutable std::mutex m_cacheMutex;
    
    // Loading queue
    std::priority_queue<TextureRequest, std::vector<TextureRequest>, TextureRequestComparator> m_loadingQueue;
    std::mutex m_queueMutex;
    
    // Background loading
    std::vector<std::thread> m_loadingThreads;
    std::atomic<bool> m_shouldStop{false};
    
    // Statistics
    mutable std::atomic<size_t> m_cacheHits{0};
    mutable std::atomic<size_t> m_cacheMisses{0};
    mutable std::atomic<size_t> m_totalMemoryUsage{0};
    
    // Current camera position for distance calculations
    Vec3 m_cameraPosition{0.0f};
    float m_currentTime = 0.0f;
    
    /**
     * @brief Background loading thread function
     */
    void loadingThreadFunction();
    
    /**
     * @brief Load texture from file
     * @param path Texture path
     * @return Loaded texture or nullptr on error
     */
    std::shared_ptr<ImageTexture> loadTextureFromFile(const std::string& path);
    
    /**
     * @brief Calculate texture priority based on distance and importance
     * @param worldPos Texture world position
     * @param importance Importance factor
     * @return Calculated priority
     */
    TexturePriority calculatePriority(const Vec3& worldPos, float importance) const;
    
    /**
     * @brief Check if texture should be unloaded
     * @param entry Cache entry
     * @return True if should unload
     */
    bool shouldUnloadTexture(const TextureCacheEntry& entry) const;
    
    /**
     * @brief Cleanup old textures to free memory
     */
    void cleanupMemory();
    
    /**
     * @brief Update texture access time
     * @param path Texture path
     */
    void updateAccessTime(const std::string& path);
};

/**
 * @brief Priority comparison for texture requests
 */
struct TextureRequestComparator {
    bool operator()(const TextureRequest& a, const TextureRequest& b) const {
        // Higher priority (lower number) comes first
        if (a.priority != b.priority) {
            return static_cast<int>(a.priority) > static_cast<int>(b.priority);
        }
        // If same priority, higher importance comes first
        return a.importance < b.importance;
    }
};

} // namespace photon
