// src/core/texture/texture.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Texture system for PBR materials

#pragma once

#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/color3.hpp"
#include "../common.hpp"
#include <memory>
#include <string>
#include <vector>

namespace photon {

/**
 * @brief Texture filtering modes
 */
enum class TextureFilter {
    NEAREST,        // Nearest neighbor
    BILINEAR,       // Bilinear interpolation
    TRILINEAR,      // Trilinear interpolation (with mipmaps)
    ANISOTROPIC     // Anisotropic filtering
};

/**
 * @brief Texture wrap modes
 */
enum class TextureWrap {
    REPEAT,         // Repeat texture
    CLAMP,          // Clamp to edge
    MIRROR,         // Mirror repeat
    BORDER          // Use border color
};

/**
 * @brief Texture format
 */
enum class TextureFormat {
    RGB8,           // 8-bit RGB
    RGBA8,          // 8-bit RGBA
    RGB16F,         // 16-bit float RGB
    RGBA16F,        // 16-bit float RGBA
    RGB32F,         // 32-bit float RGB
    RGBA32F,        // 32-bit float RGBA
    R8,             // 8-bit single channel
    R16F,           // 16-bit float single channel
    R32F,           // 32-bit float single channel

    // Compressed formats
    DXT1,           // DXT1/BC1 compression (RGB, 4:1 ratio)
    DXT5,           // DXT5/BC3 compression (RGBA, 4:1 ratio)
    BC7,            // BC7 compression (RGBA, 4:1 ratio, high quality)
    ETC2_RGB,       // ETC2 RGB compression (mobile-friendly)
    ETC2_RGBA       // ETC2 RGBA compression (mobile-friendly)
};

/**
 * @brief Texture compression quality levels
 */
enum class CompressionQuality {
    LOW,            // Fast compression, lower quality
    MEDIUM,         // Balanced compression
    HIGH,           // Slow compression, best quality
    LOSSLESS        // No quality loss (larger file size)
};

/**
 * @brief Texture compression settings
 */
struct CompressionSettings {
    CompressionQuality quality = CompressionQuality::MEDIUM;
    bool autoFormat = true;         // Automatically choose best format
    bool generateMipmaps = true;    // Generate mipmap chain
    float alphaThreshold = 0.5f;    // Alpha threshold for DXT1

    static CompressionSettings defaultSettings() {
        return CompressionSettings{CompressionQuality::MEDIUM, true, true, 0.5f};
    }
};

/**
 * @brief Abstract base texture class
 */
class Texture {
public:
    /**
     * @brief Constructor
     */
    Texture() = default;
    
    /**
     * @brief Virtual destructor
     */
    virtual ~Texture() = default;
    
    /**
     * @brief Sample texture at UV coordinates
     * @param uv UV coordinates [0,1]
     * @return Sampled color
     */
    virtual Color3 sample(const Vec2& uv) const = 0;
    
    /**
     * @brief Sample texture with derivatives for filtering
     * @param uv UV coordinates
     * @param dudx Partial derivative du/dx
     * @param dudy Partial derivative du/dy
     * @param dvdx Partial derivative dv/dx
     * @param dvdy Partial derivative dv/dy
     * @return Sampled color
     */
    virtual Color3 sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const {
        // Default implementation ignores derivatives
        return sample(uv);
    }
    
    /**
     * @brief Sample single channel (for roughness, metallic, etc.)
     * @param uv UV coordinates
     * @return Single channel value
     */
    virtual float sampleFloat(const Vec2& uv) const {
        Color3 color = sample(uv);
        return color.luminance(); // Use luminance as default
    }
    
    /**
     * @brief Get texture width
     */
    virtual int getWidth() const = 0;
    
    /**
     * @brief Get texture height
     */
    virtual int getHeight() const = 0;
    
    /**
     * @brief Get texture format
     */
    virtual TextureFormat getFormat() const = 0;
    
    /**
     * @brief Check if texture has alpha channel
     */
    virtual bool hasAlpha() const = 0;
    
    /**
     * @brief Set texture filtering mode
     */
    virtual void setFilter(TextureFilter filter) { m_filter = filter; }
    
    /**
     * @brief Set texture wrap mode
     */
    virtual void setWrap(TextureWrap wrap) { m_wrap = wrap; }
    
    /**
     * @brief Get texture name/path
     */
    const std::string& getName() const { return m_name; }
    
    /**
     * @brief Set texture name/path
     */
    void setName(const std::string& name) { m_name = name; }

protected:
    std::string m_name;
    TextureFilter m_filter = TextureFilter::BILINEAR;
    TextureWrap m_wrap = TextureWrap::REPEAT;
    
    /**
     * @brief Apply wrap mode to UV coordinate
     */
    float applyWrap(float coord) const;
    
    /**
     * @brief Apply wrap mode to UV coordinates
     */
    Vec2 applyWrap(const Vec2& uv) const;
};

/**
 * @brief Image-based texture
 */
class ImageTexture : public Texture {
public:
    /**
     * @brief Constructor
     */
    ImageTexture() = default;
    
    /**
     * @brief Constructor with image data
     * @param width Image width
     * @param height Image height
     * @param channels Number of channels (1, 3, or 4)
     * @param data Image data (float array)
     */
    ImageTexture(int width, int height, int channels, const float* data);
    
    /**
     * @brief Load from file
     * @param filename Image file path
     * @return True if successful
     */
    bool loadFromFile(const std::string& filename);
    
    /**
     * @brief Create from color
     * @param color Solid color
     * @return Texture with solid color
     */
    static std::shared_ptr<ImageTexture> createSolid(const Color3& color);
    
    /**
     * @brief Create from single value (for roughness, metallic, etc.)
     * @param value Single channel value
     * @return Texture with solid value
     */
    static std::shared_ptr<ImageTexture> createSolid(float value);
    
    // Texture interface implementation
    Color3 sample(const Vec2& uv) const override;
    Color3 sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const override;
    float sampleFloat(const Vec2& uv) const override;
    
    int getWidth() const override { return m_width; }
    int getHeight() const override { return m_height; }
    TextureFormat getFormat() const override { return m_format; }
    bool hasAlpha() const override { return m_channels == 4; }
    
    /**
     * @brief Get raw pixel data
     */
    const std::vector<float>& getData() const { return m_data; }
    
    /**
     * @brief Set gamma correction
     */
    void setGamma(float gamma) { m_gamma = gamma; }

    /**
     * @brief Compress texture using specified settings
     * @param settings Compression settings
     * @return True if compression successful
     */
    bool compress(const CompressionSettings& settings = CompressionSettings::defaultSettings());

    /**
     * @brief Check if texture is compressed
     */
    bool isCompressed() const;

    /**
     * @brief Get compression ratio (original size / compressed size)
     */
    float getCompressionRatio() const;

    /**
     * @brief Get memory usage in bytes
     */
    size_t getMemoryUsage() const;

private:
    int m_width = 0;
    int m_height = 0;
    int m_channels = 0;
    TextureFormat m_format = TextureFormat::RGB8;
    std::vector<float> m_data;
    std::vector<uint8_t> m_compressedData;  // Compressed texture data
    float m_gamma = 2.2f;
    bool m_isCompressed = false;
    size_t m_originalSize = 0;              // Original uncompressed size
    CompressionSettings m_compressionSettings;
    
    /**
     * @brief Sample pixel at integer coordinates
     */
    Color3 samplePixel(int x, int y) const;
    
    /**
     * @brief Bilinear interpolation
     */
    Color3 sampleBilinear(float u, float v) const;
    
    /**
     * @brief Apply gamma correction
     */
    Color3 applyGamma(const Color3& color) const;

    /**
     * @brief Get texture data (protected for derived classes)
     */
    const std::vector<float>& getData() const { return m_data; }

    /**
     * @brief Get texture width (protected for derived classes)
     */
    int getTextureWidth() const { return m_width; }

    /**
     * @brief Get texture height (protected for derived classes)
     */
    int getTextureHeight() const { return m_height; }

    /**
     * @brief Get texture channels (protected for derived classes)
     */
    int getTextureChannels() const { return m_channels; }

    /**
     * @brief Choose optimal compression format based on texture content
     */
    TextureFormat chooseCompressionFormat(const CompressionSettings& settings) const;

    /**
     * @brief Compress to DXT1 format
     */
    bool compressToDXT1();

    /**
     * @brief Compress to DXT5 format
     */
    bool compressToDXT5();

    /**
     * @brief Compress to BC7 format
     */
    bool compressToBC7();

    /**
     * @brief Sample from compressed data
     */
    Color3 sampleCompressed(const Vec2& uv) const;
};

/**
 * @brief Procedural texture base class
 */
class ProceduralTexture : public Texture {
public:
    /**
     * @brief Constructor
     */
    ProceduralTexture() = default;
    
    // Default implementations
    int getWidth() const override { return 1024; }
    int getHeight() const override { return 1024; }
    TextureFormat getFormat() const override { return TextureFormat::RGB32F; }
    bool hasAlpha() const override { return false; }
};

/**
 * @brief Checkerboard texture
 */
class CheckerboardTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor
     * @param color1 First color
     * @param color2 Second color
     * @param scale Checkerboard scale
     */
    CheckerboardTexture(const Color3& color1 = Color3(1.0f), 
                       const Color3& color2 = Color3(0.0f), 
                       float scale = 8.0f);
    
    Color3 sample(const Vec2& uv) const override;

private:
    Color3 m_color1, m_color2;
    float m_scale;
};

/**
 * @brief Noise types for procedural texture generation
 */
enum class NoiseType {
    PERLIN,     ///< Classic Perlin noise (smooth gradients)
    SIMPLEX,    ///< Simplex noise (better performance, less artifacts)
    WORLEY      ///< Worley noise (cellular patterns)
};

/**
 * @brief Fractal noise parameters for multi-octave generation
 */
struct FractalParams {
    int octaves = 4;           ///< Number of octaves
    float lacunarity = 2.0f;   ///< Frequency multiplier between octaves
    float persistence = 0.5f;  ///< Amplitude multiplier between octaves
    float scale = 1.0f;        ///< Overall scale factor

    /**
     * @brief Default fractal parameters
     */
    static FractalParams defaultParams() {
        return FractalParams{4, 2.0f, 0.5f, 1.0f};
    }
};

/**
 * @brief Advanced noise texture with multiple algorithms
 */
class NoiseTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor with basic parameters
     * @param frequency Base noise frequency
     * @param amplitude Base noise amplitude
     * @param octaves Number of fractal octaves
     */
    NoiseTexture(float frequency = 4.0f, float amplitude = 1.0f, int octaves = 4);

    /**
     * @brief Constructor with advanced parameters
     * @param type Noise algorithm type
     * @param frequency Base frequency
     * @param amplitude Base amplitude
     * @param fractal Fractal noise parameters
     */
    NoiseTexture(NoiseType type, float frequency, float amplitude, const FractalParams& fractal);

    /**
     * @brief Set noise type
     */
    void setNoiseType(NoiseType type) { m_noiseType = type; }

    /**
     * @brief Set fractal parameters
     */
    void setFractalParams(const FractalParams& params) { m_fractal = params; }

    /**
     * @brief Set noise seed for variation
     */
    void setSeed(int seed) { m_seed = seed; }

    // Texture interface
    Color3 sample(const Vec2& uv) const override;
    float sampleFloat(const Vec2& uv) const override;

private:
    NoiseType m_noiseType = NoiseType::PERLIN;
    float m_frequency;
    float m_amplitude;
    FractalParams m_fractal;
    int m_seed = 0;

    /**
     * @brief Generate fractal noise using specified algorithm
     */
    float generateFractalNoise(float x, float y) const;

    /**
     * @brief Perlin noise implementation (improved)
     */
    float perlinNoise(float x, float y) const;

    /**
     * @brief Simplex noise implementation
     */
    float simplexNoise(float x, float y) const;

    /**
     * @brief Worley noise implementation (cellular)
     */
    float worleyNoise(float x, float y) const;

    /**
     * @brief Hash function for noise generation
     */
    float hash(float x, float y) const;

    /**
     * @brief Smooth interpolation function
     */
    float smoothstep(float t) const;

    /**
     * @brief Linear interpolation
     */
    float lerp(float a, float b, float t) const;
};

/**
 * @brief Pattern types for geometric pattern generation
 */
enum class PatternType {
    CHECKERBOARD,   ///< Checkerboard/chess pattern
    STRIPES,        ///< Linear stripe pattern
    DOTS,           ///< Dot/circle pattern
    GRID            ///< Grid line pattern
};

/**
 * @brief Pattern parameters for geometric pattern generation
 */
struct PatternParams {
    float scale = 1.0f;        ///< Overall pattern scale
    float rotation = 0.0f;     ///< Pattern rotation in radians
    Vec2 offset = Vec2(0.0f);  ///< Pattern offset
    float lineWidth = 0.1f;    ///< Line width for grid patterns
    float dotSize = 0.3f;      ///< Dot size for dot patterns
    bool antiAlias = true;     ///< Enable anti-aliasing

    /**
     * @brief Default pattern parameters
     */
    static PatternParams defaultParams() {
        return PatternParams{1.0f, 0.0f, Vec2(0.0f), 0.1f, 0.3f, true};
    }
};

/**
 * @brief Pattern texture for geometric pattern generation
 */
class PatternTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor with basic parameters
     * @param type Pattern type
     * @param scale Pattern scale
     * @param color1 Primary color
     * @param color2 Secondary color
     */
    PatternTexture(PatternType type, float scale = 1.0f,
                   const Color3& color1 = Color3(1.0f),
                   const Color3& color2 = Color3(0.0f));

    /**
     * @brief Constructor with advanced parameters
     * @param type Pattern type
     * @param params Pattern parameters
     * @param color1 Primary color
     * @param color2 Secondary color
     */
    PatternTexture(PatternType type, const PatternParams& params,
                   const Color3& color1 = Color3(1.0f),
                   const Color3& color2 = Color3(0.0f));

    /**
     * @brief Set pattern type
     */
    void setPatternType(PatternType type) { m_patternType = type; }

    /**
     * @brief Set pattern parameters
     */
    void setPatternParams(const PatternParams& params) { m_params = params; }

    /**
     * @brief Set pattern colors
     */
    void setColors(const Color3& color1, const Color3& color2) {
        m_color1 = color1;
        m_color2 = color2;
    }

    // Texture interface
    Color3 sample(const Vec2& uv) const override;
    float sampleFloat(const Vec2& uv) const override;

private:
    PatternType m_patternType;
    PatternParams m_params;
    Color3 m_color1;
    Color3 m_color2;

    /**
     * @brief Generate pattern value at given coordinates
     */
    float generatePattern(float x, float y) const;

    /**
     * @brief Checkerboard pattern implementation
     */
    float checkerboardPattern(float x, float y) const;

    /**
     * @brief Stripe pattern implementation
     */
    float stripePattern(float x, float y) const;

    /**
     * @brief Dot pattern implementation
     */
    float dotPattern(float x, float y) const;

    /**
     * @brief Grid pattern implementation
     */
    float gridPattern(float x, float y) const;

    /**
     * @brief Apply rotation transformation
     */
    Vec2 applyRotation(const Vec2& uv) const;

    /**
     * @brief Smooth step function for anti-aliasing
     */
    float smoothStep(float edge0, float edge1, float x) const;
};

/**
 * @brief Gradient types for gradient generation
 */
enum class GradientType {
    LINEAR,     ///< Linear gradient
    RADIAL,     ///< Radial gradient
    ANGULAR     ///< Angular/conical gradient
};

/**
 * @brief Color stop for gradient definition
 */
struct ColorStop {
    float position;     ///< Position along gradient [0,1]
    Color3 color;       ///< Color at this position

    ColorStop(float pos = 0.0f, const Color3& col = Color3(0.0f))
        : position(pos), color(col) {}
};

/**
 * @brief Gradient parameters for gradient generation
 */
struct GradientParams {
    Vec2 startPoint = Vec2(0.0f, 0.0f);    ///< Start point for linear gradients
    Vec2 endPoint = Vec2(1.0f, 1.0f);      ///< End point for linear gradients
    Vec2 center = Vec2(0.5f, 0.5f);        ///< Center for radial/angular gradients
    float radius = 0.5f;                    ///< Radius for radial gradients
    float angle = 0.0f;                     ///< Angle for linear/angular gradients
    float spread = 1.0f;                    ///< Spread factor for gradients
    bool repeat = false;                    ///< Repeat gradient pattern

    /**
     * @brief Default gradient parameters
     */
    static GradientParams defaultParams() {
        return GradientParams{Vec2(0.0f, 0.5f), Vec2(1.0f, 0.5f), Vec2(0.5f, 0.5f),
                             0.5f, 0.0f, 1.0f, false};
    }
};

/**
 * @brief Gradient texture for gradient generation
 */
class GradientTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor with basic parameters
     * @param type Gradient type
     * @param color1 Start color
     * @param color2 End color
     */
    GradientTexture(GradientType type,
                    const Color3& color1 = Color3(0.0f),
                    const Color3& color2 = Color3(1.0f));

    /**
     * @brief Constructor with advanced parameters
     * @param type Gradient type
     * @param params Gradient parameters
     * @param colorStops Color stops array
     */
    GradientTexture(GradientType type, const GradientParams& params,
                    const std::vector<ColorStop>& colorStops);

    /**
     * @brief Set gradient type
     */
    void setGradientType(GradientType type) { m_gradientType = type; }

    /**
     * @brief Set gradient parameters
     */
    void setGradientParams(const GradientParams& params) { m_params = params; }

    /**
     * @brief Set color stops
     */
    void setColorStops(const std::vector<ColorStop>& stops) { m_colorStops = stops; }

    /**
     * @brief Add color stop
     */
    void addColorStop(float position, const Color3& color) {
        m_colorStops.emplace_back(position, color);
        sortColorStops();
    }

    /**
     * @brief Clear color stops
     */
    void clearColorStops() { m_colorStops.clear(); }

    // Texture interface
    Color3 sample(const Vec2& uv) const override;
    float sampleFloat(const Vec2& uv) const override;

private:
    GradientType m_gradientType;
    GradientParams m_params;
    std::vector<ColorStop> m_colorStops;

    /**
     * @brief Generate gradient value at given coordinates
     */
    float generateGradientValue(float x, float y) const;

    /**
     * @brief Linear gradient implementation
     */
    float linearGradient(float x, float y) const;

    /**
     * @brief Radial gradient implementation
     */
    float radialGradient(float x, float y) const;

    /**
     * @brief Angular gradient implementation
     */
    float angularGradient(float x, float y) const;

    /**
     * @brief Interpolate color from color stops
     */
    Color3 interpolateColorStops(float t) const;

    /**
     * @brief Sort color stops by position
     */
    void sortColorStops();

    /**
     * @brief Apply repeat mode to gradient value
     */
    float applyRepeat(float t) const;
};

} // namespace photon
