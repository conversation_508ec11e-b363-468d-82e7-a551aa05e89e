// src/core/renderer.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Main renderer implementation

#include "renderer.hpp"
#include "sampler/sampler.hpp"
#include "image/image_io.hpp"
#include "denoising/ai_denoiser.hpp"
#include <chrono>
#include <iostream>

namespace photon {

Renderer::Renderer() {
    initializeEmbree();
}

Renderer::~Renderer() {
    // Device is managed globally, don't release here
}

void Renderer::initializeEmbree() {
    // Get global Embree device
    m_device = getEmbreeDevice();
    if (!m_device) {
        std::cerr << "Embree device not initialized. Call photon::initialize() first." << std::endl;
        throw std::runtime_error("Embree initialization failed");
    }
}

void Renderer::setScene(std::shared_ptr<Scene> scene) {
    m_scene = scene;
    if (m_scene) {
        buildAccelerationStructure();
    }
}

void Renderer::setCamera(std::shared_ptr<Camera> camera) {
    m_camera = camera;
}

void Renderer::setIntegrator(std::shared_ptr<Integrator> integrator) {
    m_integrator = integrator;
}

void Renderer::setSettings(const RenderSettings& settings) {
    m_settings = settings;
    m_film = std::make_unique<Film>(settings.width, settings.height);

    // Initialize adaptive sampling if enabled
    if (settings.adaptiveSampling) {
        initializeAdaptiveSampling();
    }

    // Initialize advanced memory management
    enableAdvancedMemoryManagement(true);
}

void Renderer::buildAccelerationStructure() {
    if (!m_scene) return;
    
    // Build Embree BVH
    RTCScene rtcScene = rtcNewScene(m_device);
    
    // Use Scene's buildAccelerationStructure method
    m_scene->buildAccelerationStructure(m_device);

}

void Renderer::render() {
    if (!m_scene || !m_camera || !m_integrator || !m_film) {
        throw std::runtime_error("Renderer not properly configured");
    }

    PHOTON_PROFILE_SCOPE("Renderer::render");

    m_isRendering = true;
    m_shouldStop = false;

    // Reset stats
    m_stats.renderedTiles = 0;
    m_stats.totalTiles = 0;
    m_stats.totalSamples = 0;
    m_stats.renderTime = 0.0f;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Calculate tiles
    int tilesX = (m_settings.width + m_settings.tileSize - 1) / m_settings.tileSize;
    int tilesY = (m_settings.height + m_settings.tileSize - 1) / m_settings.tileSize;
    m_stats.totalTiles = tilesX * tilesY;
    
    // Render tiles in parallel
    {
        PHOTON_PROFILE_SCOPE("Parallel_Tile_Rendering");
        tbb::parallel_for(
            tbb::blocked_range2d<int>(0, tilesY, 1, 0, tilesX, 1),
            [this](const tbb::blocked_range2d<int>& range) {
                for (int ty = range.rows().begin(); ty < range.rows().end(); ++ty) {
                    for (int tx = range.cols().begin(); tx < range.cols().end(); ++tx) {
                        if (m_shouldStop) return;
                        renderTile(tx, ty);
                    }
                }
            }
        );
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    m_stats.renderTime = std::chrono::duration<float>(endTime - startTime).count();

    // Apply denoising if enabled
    if (m_settings.enableDenoising && AIDenoiser::isAvailable()) {
        auto denoisingStart = std::chrono::high_resolution_clock::now();

        DenoisingSettings denoisingSettings = m_settings.getDenoisingSettings();
        denoisingSettings.verbose = true;

        auto& denoiser = DenoisingManager::getInstance();
        if (denoiser.denoise(*m_film, denoisingSettings)) {
            auto denoisingEnd = std::chrono::high_resolution_clock::now();
            float denoisingTime = std::chrono::duration<float>(denoisingEnd - denoisingStart).count();

            std::cout << "[Renderer] AI Denoising completed in " << denoisingTime * 1000.0f << "ms" << std::endl;
        } else {
            std::cerr << "[Renderer] AI Denoising failed" << std::endl;
        }
    }

    m_isRendering = false;

    // Final progress callback
    if (m_progressCallback) {
        m_progressCallback(1.0f, m_stats);
    }
}

void Renderer::renderTile(int tileX, int tileY) {
    int x0 = tileX * m_settings.tileSize;
    int y0 = tileY * m_settings.tileSize;
    int x1 = std::min(x0 + m_settings.tileSize, m_settings.width);
    int y1 = std::min(y0 + m_settings.tileSize, m_settings.height);

    // Use adaptive sampling if enabled
    if (m_settings.adaptiveSampling && m_adaptiveSampler) {
        renderTileAdaptive(x0, y0, x1 - x0, y1 - y0);
    } else {
        renderTileInternal(x0, y0, x1 - x0, y1 - y0);
    }
    
    m_stats.renderedTiles++;
    
    // Progress callback
    if (m_progressCallback) {
        m_progressCallback(m_stats.getProgress(), m_stats);
    }
    
    // Tile callback with pixel data
    if (m_tileCallback) {
        std::vector<float> tilePixels((x1 - x0) * (y1 - y0) * 3);
        for (int y = y0; y < y1; ++y) {
            for (int x = x0; x < x1; ++x) {
                Color3 pixel = m_film->getPixel(x, y);
                int idx = ((y - y0) * (x1 - x0) + (x - x0)) * 3;
                tilePixels[idx + 0] = pixel.x;
                tilePixels[idx + 1] = pixel.y;
                tilePixels[idx + 2] = pixel.z;
            }
        }
        m_tileCallback(x0, y0, x1 - x0, y1 - y0, tilePixels.data());
    }
}

void Renderer::renderTileInternal(int x0, int y0, int width, int height) {
    PHOTON_PROFILE_SCOPE("Renderer::renderTileInternal");

    // Create sampler for this tile
    auto sampler = std::make_unique<RandomSampler>(x0 + y0 * m_settings.width);

    // Render each pixel in tile
    for (int y = y0; y < y0 + height; ++y) {
        for (int x = x0; x < x0 + width; ++x) {
            // Multiple samples per pixel
            Color3 pixelColor(0);
            for (int s = 0; s < m_settings.samplesPerPixel; ++s) {
                pixelColor += samplePixel(x, y, *sampler);
                m_stats.totalSamples++;
            }
            pixelColor /= float(m_settings.samplesPerPixel);

            // Store in film
            m_film->addSample(x, y, pixelColor);
        }
    }
}

Color3 Renderer::samplePixel(int x, int y, Sampler& sampler) {
    // Generate camera ray
    float u = (x + sampler.get1D()) / float(m_settings.width);
    float v = (y + sampler.get1D()) / float(m_settings.height);
    Ray ray = m_camera->generateRay(u, v, sampler);
    
    // Trace ray through scene
    return m_integrator->Li(ray, *m_scene, sampler);
}

void Renderer::stop() {
    m_shouldStop = true;
}

bool Renderer::saveImage(const std::string& filename, int quality) const {
    if (!m_film) {
        std::cerr << "Error: No film data to save" << std::endl;
        return false;
    }

    return ImageIO::saveFromFilm(filename, *m_film, quality);
}

DenoisingSettings RenderSettings::getDenoisingSettings() const {
    DenoisingSettings settings;

    settings.enabled = enableDenoising;
    settings.strength = denoisingStrength;
    settings.useAlbedo = denoisingUseAlbedo;
    settings.useNormals = denoisingUseNormals;

    // Convert quality level
    switch (denoisingQuality) {
        case 0: settings.quality = DenoisingQuality::FAST; break;
        case 1: settings.quality = DenoisingQuality::BALANCED; break;
        case 2: settings.quality = DenoisingQuality::HIGH; break;
        case 3: settings.quality = DenoisingQuality::ULTRA; break;
        default: settings.quality = DenoisingQuality::BALANCED; break;
    }

    // Set tile size based on render tile size
    settings.tileSize = std::max(tileSize * 2, 256); // Larger tiles for denoising

    return settings;
}

void Renderer::initializeAdaptiveSampling() {
    if (!m_adaptiveSampler) {
        m_adaptiveSampler = std::make_unique<AdaptiveSampler>();
    }

    // Configure adaptive sampling parameters
    AdaptiveSamplingParams params;
    params.enabled = m_settings.adaptiveSampling;
    params.convergenceThreshold = m_settings.adaptiveThreshold;
    params.varianceThreshold = m_settings.adaptiveVarianceThreshold;
    params.minSamples = m_settings.adaptiveMinSamples;
    params.maxSamples = m_settings.adaptiveMaxSamples;
    params.checkInterval = m_settings.adaptiveCheckInterval;
    params.useVarianceAnalysis = m_settings.adaptiveUseVariance;
    params.useNoiseAnalysis = m_settings.adaptiveUseNoise;

    // Initialize with current image dimensions
    m_adaptiveSampler->initialize(m_settings.width, m_settings.height, params);
}

float Renderer::getAdaptiveConvergenceProgress() const {
    if (!m_adaptiveSampler) return 0.0f;
    return m_adaptiveSampler->getConvergenceProgress();
}

float Renderer::getAdaptiveEfficiency() const {
    if (!m_adaptiveSampler) return 1.0f;
    return m_adaptiveSampler->getEfficiency();
}

void Renderer::renderTileAdaptive(int x0, int y0, int width, int height) {
    // Create sampler for this tile
    auto sampler = std::make_unique<RandomSampler>(x0 + y0 * m_settings.width);

    // Render each pixel in tile with adaptive sampling
    for (int y = y0; y < y0 + height; ++y) {
        for (int x = x0; x < x0 + width; ++x) {
            Color3 pixelColor(0);
            int sampleCount = 0;

            // Start with minimum samples
            for (int s = 0; s < m_settings.adaptiveMinSamples; ++s) {
                Color3 sample = samplePixelAdaptive(x, y, *sampler);
                pixelColor += sample;
                sampleCount++;

                // Update adaptive sampler
                bool needsMoreSamples = m_adaptiveSampler->updatePixel(x, y, sample);

                m_stats.totalSamples++;

                // Check convergence periodically
                if (s >= m_settings.adaptiveMinSamples - 1 &&
                    (s + 1) % m_settings.adaptiveCheckInterval == 0) {

                    if (!needsMoreSamples || m_adaptiveSampler->hasConverged(x, y)) {
                        break;
                    }

                    // Check if we've reached max samples
                    if (sampleCount >= m_settings.adaptiveMaxSamples) {
                        break;
                    }
                }
            }

            // Continue sampling if needed and not converged
            while (sampleCount < m_settings.adaptiveMaxSamples &&
                   !m_adaptiveSampler->hasConverged(x, y)) {

                Color3 sample = samplePixelAdaptive(x, y, *sampler);
                pixelColor += sample;
                sampleCount++;

                // Update adaptive sampler
                bool needsMoreSamples = m_adaptiveSampler->updatePixel(x, y, sample);

                m_stats.totalSamples++;

                // Check convergence
                if ((sampleCount % m_settings.adaptiveCheckInterval == 0) &&
                    (!needsMoreSamples || m_adaptiveSampler->hasConverged(x, y))) {
                    break;
                }
            }

            // Average and store in film
            if (sampleCount > 0) {
                pixelColor /= static_cast<float>(sampleCount);
                m_film->addSample(x, y, pixelColor);
            }
        }
    }
}

Color3 Renderer::samplePixelAdaptive(int x, int y, Sampler& sampler) {
    // Same as regular sampling but with potential for future optimizations
    return samplePixel(x, y, sampler);
}

void Renderer::enableProfiling(bool enable) {
    PerformanceProfiler::getInstance().setEnabled(enable);
    if (enable) {
        PerformanceProfiler::getInstance().initialize();
    }
}

std::string Renderer::getPerformanceReport() const {
    return PerformanceProfiler::getInstance().generateReport();
}

std::string Renderer::getPerformanceHTMLReport() const {
    return PerformanceProfiler::getInstance().generateHTMLReport();
}

const std::unordered_map<std::string, PerformanceMetric>& Renderer::getPerformanceMetrics() const {
    return PerformanceProfiler::getInstance().getMetrics();
}

void Renderer::enableAdvancedMemoryManagement(bool enable) {
    if (enable) {
        AdvancedMemoryManager::getInstance().initialize();
    } else {
        AdvancedMemoryManager::getInstance().shutdown();
    }
}

void Renderer::setMemoryAllocationStrategy(AllocationStrategy strategy) {
    AdvancedMemoryManager::getInstance().setAllocationStrategy(strategy);
}

void Renderer::runMemoryGarbageCollection() {
    size_t reclaimed = AdvancedMemoryManager::getInstance().runGarbageCollection();
    PHOTON_PROFILE_GAUGE("Memory_GC_Reclaimed", reclaimed);
}

MemoryStatistics Renderer::getMemoryStatistics() const {
    return AdvancedMemoryManager::getInstance().getStatistics();
}

std::string Renderer::getMemoryReport() const {
    return AdvancedMemoryManager::getInstance().generateMemoryReport();
}

} // namespace photon
