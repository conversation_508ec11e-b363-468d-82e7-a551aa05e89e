// src/test_texture_memory.cpp
// PhotonRender - Texture Memory Management Test
// Tests cache system, garbage collection, and memory monitoring

#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include <thread>
#include "core/texture/texture_memory.hpp"
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/color3.hpp"

using namespace photon;

/**
 * @brief Test results structure
 */
struct TestResult {
    std::string name;
    bool passed;
    double duration_ns;
    std::string details;
    
    TestResult(const std::string& n, bool p, double d, const std::string& det = "")
        : name(n), passed(p), duration_ns(d), details(det) {}
};

/**
 * @brief Test suite for texture memory management
 */
class TextureMemoryTestSuite {
private:
    std::vector<TestResult> results;
    
    void addTestResult(const std::string& name, bool passed, double duration_ns = 0.0, const std::string& details = "") {
        results.push_back(TestResult(name, passed, duration_ns, details));
    }
    
    /**
     * @brief Create test texture with specified size
     */
    std::shared_ptr<ImageTexture> createTestTexture(int width, int height, int channels = 3) {
        std::vector<float> data(width * height * channels, 0.5f);
        
        // Create simple pattern
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int index = (y * width + x) * channels;
                data[index] = static_cast<float>(x) / width;
                if (channels > 1) data[index + 1] = static_cast<float>(y) / height;
                if (channels > 2) data[index + 2] = 0.5f;
            }
        }
        
        return std::make_shared<ImageTexture>(width, height, channels, data.data());
    }

public:
    /**
     * @brief Test 1: Memory manager initialization
     */
    bool testMemoryManagerInit() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto& manager = TextureMemoryManager::getInstance();
            
            // Test default initialization
            MemorySettings defaultSettings = MemorySettings::defaultSettings();
            bool test1 = manager.initialize(defaultSettings);
            
            // Test settings
            bool test2 = (manager.getMemorySettings().maxCacheSize == defaultSettings.maxCacheSize);
            bool test3 = (manager.getCachedTextureCount() == 0);
            bool test4 = (manager.getTotalCacheMemory() == 0);
            
            bool passed = test1 && test2 && test3 && test4;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Cache limit: " + std::to_string(defaultSettings.maxCacheSize / (1024 * 1024)) + "MB";
            addTestResult("3.4.1 Memory Manager Initialization", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.4.1 Memory Manager Initialization", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 2: Texture caching
     */
    bool testTextureCaching() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto& manager = TextureMemoryManager::getInstance();
            
            // Create test textures
            auto texture1 = createTestTexture(64, 64, 3);
            auto texture2 = createTestTexture(128, 128, 3);
            auto texture3 = createTestTexture(32, 32, 4);
            
            // Test caching
            bool test1 = manager.cacheTexture("texture1", texture1);
            bool test2 = manager.cacheTexture("texture2", texture2);
            bool test3 = manager.cacheTexture("texture3", texture3);
            
            // Test retrieval
            auto retrieved1 = manager.getCachedTexture("texture1");
            auto retrieved2 = manager.getCachedTexture("texture2");
            auto retrieved3 = manager.getCachedTexture("texture3");
            
            bool test4 = (retrieved1 == texture1);
            bool test5 = (retrieved2 == texture2);
            bool test6 = (retrieved3 == texture3);
            
            // Test cache miss
            auto missing = manager.getCachedTexture("nonexistent");
            bool test7 = (missing == nullptr);
            
            // Test cache count
            bool test8 = (manager.getCachedTextureCount() == 3);
            
            bool passed = test1 && test2 && test3 && test4 && test5 && test6 && test7 && test8;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Cached: " + std::to_string(manager.getCachedTextureCount()) + 
                                 " textures, Memory: " + std::to_string(manager.getTotalCacheMemory() / 1024) + "KB";
            addTestResult("3.4.2 Texture Caching", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.4.2 Texture Caching", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 3: Cache metrics and statistics
     */
    bool testCacheMetrics() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto& manager = TextureMemoryManager::getInstance();
            
            // Clear cache and reset metrics
            manager.clearCache();
            
            auto texture = createTestTexture(64, 64, 3);
            
            // Generate some cache activity
            manager.cacheTexture("test_texture", texture);
            
            // Multiple accesses (hits)
            for (int i = 0; i < 10; ++i) {
                manager.getCachedTexture("test_texture");
            }
            
            // Cache miss
            manager.getCachedTexture("nonexistent");
            
            // Get metrics
            auto metrics = manager.getCacheMetrics();
            auto memStats = manager.getMemoryStats();
            
            // Test metrics
            bool test1 = (metrics.hits >= 10);
            bool test2 = (metrics.misses >= 1);
            bool test3 = (metrics.getHitRatio() > 0.8); // Should be > 80%
            
            // Test memory stats
            bool test4 = (memStats.numTextures > 0);
            bool test5 = (memStats.totalUsed > 0);
            bool test6 = (memStats.getEfficiency() > 0.0);
            
            bool passed = test1 && test2 && test3 && test4 && test5 && test6;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Hit ratio: " + std::to_string(metrics.getHitRatio() * 100) + 
                                 "%, Efficiency: " + std::to_string(memStats.getEfficiency() * 100) + "%";
            addTestResult("3.4.3 Cache Metrics", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.4.3 Cache Metrics", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 4: Garbage collection
     */
    bool testGarbageCollection() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto& manager = TextureMemoryManager::getInstance();
            
            // Set low memory settings for testing
            MemorySettings lowMemSettings = MemorySettings::lowMemorySettings();
            lowMemSettings.maxCacheSize = 1024 * 1024; // 1MB limit
            lowMemSettings.gcThreshold = 0.5; // 50% threshold
            manager.setMemorySettings(lowMemSettings);
            
            manager.clearCache();
            
            // Fill cache with textures
            std::vector<std::shared_ptr<ImageTexture>> textures;
            for (int i = 0; i < 10; ++i) {
                auto texture = createTestTexture(128, 128, 3); // ~192KB each
                textures.push_back(texture);
                manager.cacheTexture("texture_" + std::to_string(i), texture);
            }
            
            size_t memoryBefore = manager.getTotalCacheMemory();
            
            // Force garbage collection
            size_t freedMemory = manager.forceGarbageCollection();
            
            size_t memoryAfter = manager.getTotalCacheMemory();
            
            // Test GC effectiveness
            bool test1 = (freedMemory > 0);
            bool test2 = (memoryAfter < memoryBefore);
            bool test3 = (manager.getCachedTextureCount() < 10);
            
            // Test automatic GC trigger
            bool test4 = !manager.needsGarbageCollection(); // Should be false after GC
            
            bool passed = test1 && test2 && test3 && test4;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Freed: " + std::to_string(freedMemory / 1024) + 
                                 "KB, Remaining: " + std::to_string(manager.getCachedTextureCount()) + " textures";
            addTestResult("3.4.4 Garbage Collection", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.4.4 Garbage Collection", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 5: Texture pinning
     */
    bool testTexturePinning() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto& manager = TextureMemoryManager::getInstance();
            manager.clearCache();
            
            auto texture1 = createTestTexture(64, 64, 3);
            auto texture2 = createTestTexture(64, 64, 3);
            
            // Cache textures
            manager.cacheTexture("pinned_texture", texture1);
            manager.cacheTexture("normal_texture", texture2);
            
            // Pin one texture
            bool test1 = manager.pinTexture("pinned_texture");
            bool test2 = !manager.pinTexture("nonexistent"); // Should fail
            
            // Force aggressive GC
            MemorySettings aggressiveSettings;
            aggressiveSettings.maxCacheSize = 1024; // Very small cache
            aggressiveSettings.gcTargetRatio = 0.1;
            manager.setMemorySettings(aggressiveSettings);
            
            manager.forceGarbageCollection();
            
            // Check that pinned texture survives GC
            auto pinnedTexture = manager.getCachedTexture("pinned_texture");
            auto normalTexture = manager.getCachedTexture("normal_texture");
            
            bool test3 = (pinnedTexture != nullptr); // Should survive
            bool test4 = (normalTexture == nullptr); // Should be evicted
            
            // Unpin texture
            bool test5 = manager.unpinTexture("pinned_texture");
            
            bool passed = test1 && test2 && test3 && test4 && test5;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.4.5 Texture Pinning", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.4.5 Texture Pinning", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 6: Performance benchmark
     */
    bool testPerformance() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto& manager = TextureMemoryManager::getInstance();
            
            // Reset to default settings
            manager.setMemorySettings(MemorySettings::defaultSettings());
            manager.clearCache();
            
            const int numTextures = 100;
            const int numAccesses = 1000;
            
            // Create textures
            std::vector<std::shared_ptr<ImageTexture>> textures;
            for (int i = 0; i < numTextures; ++i) {
                textures.push_back(createTestTexture(32, 32, 3));
            }
            
            // Benchmark caching
            auto cacheStart = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < numTextures; ++i) {
                manager.cacheTexture("perf_texture_" + std::to_string(i), textures[i]);
            }
            auto cacheEnd = std::chrono::high_resolution_clock::now();
            
            double cacheTime = std::chrono::duration<double, std::nano>(cacheEnd - cacheStart).count();
            
            // Benchmark access
            auto accessStart = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < numAccesses; ++i) {
                int textureIndex = i % numTextures;
                manager.getCachedTexture("perf_texture_" + std::to_string(textureIndex));
            }
            auto accessEnd = std::chrono::high_resolution_clock::now();
            
            double accessTime = std::chrono::duration<double, std::nano>(accessEnd - accessStart).count();
            
            // Performance targets
            double avgCacheTime = cacheTime / numTextures;
            double avgAccessTime = accessTime / numAccesses;
            
            bool test1 = avgCacheTime < 100000.0; // < 100μs per cache operation
            bool test2 = avgAccessTime < 10000.0;  // < 10μs per access
            
            // Test final metrics
            auto metrics = manager.getCacheMetrics();
            bool test3 = metrics.getHitRatio() > 0.9; // > 90% hit ratio
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Cache: " + std::to_string(avgCacheTime / 1000.0) + 
                                 "μs, Access: " + std::to_string(avgAccessTime / 1000.0) + 
                                 "μs, Hit ratio: " + std::to_string(metrics.getHitRatio() * 100) + "%";
            addTestResult("3.4.6 Performance Benchmark", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.4.6 Performance Benchmark", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Run all tests
     */
    bool runAllTests() {
        std::cout << "=== PhotonRender Texture Memory Management Test Suite ===" << std::endl;
        std::cout << "Testing cache system, GC, and memory monitoring..." << std::endl << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testMemoryManagerInit();
        allPassed &= testTextureCaching();
        allPassed &= testCacheMetrics();
        allPassed &= testGarbageCollection();
        allPassed &= testTexturePinning();
        allPassed &= testPerformance();
        
        return allPassed;
    }
    
    /**
     * @brief Print test results
     */
    void printResults() {
        std::cout << std::endl << "=== TEST RESULTS ===" << std::endl;
        
        int passed = 0;
        int total = results.size();
        
        for (const auto& result : results) {
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.name;
            
            if (result.duration_ns > 0) {
                std::cout << " (" << std::fixed << std::setprecision(2) 
                          << result.duration_ns / 1000.0 << "μs)";
            }
            
            if (!result.details.empty()) {
                std::cout << " - " << result.details;
            }
            
            std::cout << std::endl;
            
            if (result.passed) passed++;
        }
        
        std::cout << std::endl << "Summary: " << passed << "/" << total 
                  << " tests passed (" << (100.0 * passed / total) << "%)" << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Texture memory management system is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    TextureMemoryTestSuite testSuite;
    
    bool success = testSuite.runAllTests();
    testSuite.printResults();
    
    // Cleanup
    TextureMemoryManager::getInstance().shutdown();
    
    return success ? 0 : 1;
}
