// src/test_image_generation.cpp
// PhotonRender - Test Image Generation
// Test diretto della generazione e salvataggio immagini

#include <iostream>
#include <vector>
#include <cmath>

// PhotonRender headers
#include "core/image/image_io.hpp"
#include "core/math/color3.hpp"

using namespace photon;

/**
 * @brief Test diretto di generazione immagini
 */
int main() {
    std::cout << "=== PhotonRender Image Generation Test ===" << std::endl;
    std::cout << "Test diretto delle capacità di generazione immagini" << std::endl;
    std::cout << "=================================================" << std::endl;
    std::cout << std::endl;

    try {
        const int width = 512;
        const int height = 512;
        
        // Test 1: Gradient Image
        std::cout << "🌈 Generando immagine gradient..." << std::endl;
        
        ImageData gradientImage;
        gradientImage.width = width;
        gradientImage.height = height;
        gradientImage.channels = 3;
        gradientImage.data.resize(width * height * 3);
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                float r = static_cast<float>(x) / static_cast<float>(width - 1);
                float g = static_cast<float>(y) / static_cast<float>(height - 1);
                float b = 0.5f;
                
                int index = (y * width + x) * 3;
                gradientImage.data[index + 0] = static_cast<unsigned char>(r * 255);
                gradientImage.data[index + 1] = static_cast<unsigned char>(g * 255);
                gradientImage.data[index + 2] = static_cast<unsigned char>(b * 255);
            }
        }
        
        bool saved1 = ImageIO::saveImage("test_gradient.png", gradientImage);
        std::cout << "   💾 Gradient salvato: " << (saved1 ? "test_gradient.png ✓" : "Errore ✗") << std::endl;
        
        // Test 2: Checkerboard Pattern
        std::cout << "🏁 Generando pattern checkerboard..." << std::endl;
        
        ImageData checkerImage;
        checkerImage.width = width;
        checkerImage.height = height;
        checkerImage.channels = 3;
        checkerImage.data.resize(width * height * 3);
        
        const int checkSize = 32;
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                bool isWhite = ((x / checkSize) + (y / checkSize)) % 2 == 0;
                unsigned char value = isWhite ? 255 : 0;
                
                int index = (y * width + x) * 3;
                checkerImage.data[index + 0] = value;
                checkerImage.data[index + 1] = value;
                checkerImage.data[index + 2] = value;
            }
        }
        
        bool saved2 = ImageIO::saveImage("test_checkerboard.png", checkerImage);
        std::cout << "   💾 Checkerboard salvato: " << (saved2 ? "test_checkerboard.png ✓" : "Errore ✗") << std::endl;
        
        // Test 3: Circle Pattern
        std::cout << "⭕ Generando pattern cerchi..." << std::endl;
        
        ImageData circleImage;
        circleImage.width = width;
        circleImage.height = height;
        circleImage.channels = 3;
        circleImage.data.resize(width * height * 3);
        
        float centerX = width * 0.5f;
        float centerY = height * 0.5f;
        float maxRadius = std::min(width, height) * 0.4f;
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                float dx = x - centerX;
                float dy = y - centerY;
                float distance = std::sqrt(dx * dx + dy * dy);
                
                float intensity = 1.0f - std::min(distance / maxRadius, 1.0f);
                intensity = std::max(0.0f, intensity);
                
                unsigned char r = static_cast<unsigned char>(intensity * 255);
                unsigned char g = static_cast<unsigned char>(intensity * 128);
                unsigned char b = static_cast<unsigned char>(intensity * 64);
                
                int index = (y * width + x) * 3;
                circleImage.data[index + 0] = r;
                circleImage.data[index + 1] = g;
                circleImage.data[index + 2] = b;
            }
        }
        
        bool saved3 = ImageIO::saveImage("test_circle.png", circleImage);
        std::cout << "   💾 Circle salvato: " << (saved3 ? "test_circle.png ✓" : "Errore ✗") << std::endl;
        
        // Test 4: Noise Pattern
        std::cout << "🌊 Generando pattern noise..." << std::endl;
        
        ImageData noiseImage;
        noiseImage.width = width;
        noiseImage.height = height;
        noiseImage.channels = 3;
        noiseImage.data.resize(width * height * 3);
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                // Simple noise function
                float noise = std::sin(x * 0.1f) * std::cos(y * 0.1f) * 0.5f + 0.5f;
                noise += std::sin(x * 0.05f + y * 0.05f) * 0.3f;
                noise = std::max(0.0f, std::min(1.0f, noise));
                
                unsigned char value = static_cast<unsigned char>(noise * 255);
                
                int index = (y * width + x) * 3;
                noiseImage.data[index + 0] = value;
                noiseImage.data[index + 1] = static_cast<unsigned char>(value * 0.8f);
                noiseImage.data[index + 2] = static_cast<unsigned char>(value * 0.6f);
            }
        }
        
        bool saved4 = ImageIO::saveImage("test_noise.png", noiseImage);
        std::cout << "   💾 Noise salvato: " << (saved4 ? "test_noise.png ✓" : "Errore ✗") << std::endl;
        
        // Test 5: Color Spectrum
        std::cout << "🎨 Generando spettro colori..." << std::endl;
        
        ImageData spectrumImage;
        spectrumImage.width = width;
        spectrumImage.height = height;
        spectrumImage.channels = 3;
        spectrumImage.data.resize(width * height * 3);
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                float hue = static_cast<float>(x) / static_cast<float>(width - 1) * 360.0f;
                float saturation = 1.0f;
                float value = static_cast<float>(height - y) / static_cast<float>(height - 1);
                
                // HSV to RGB conversion
                float c = value * saturation;
                float h_prime = hue / 60.0f;
                float x_val = c * (1.0f - std::abs(std::fmod(h_prime, 2.0f) - 1.0f));
                float m = value - c;
                
                float r, g, b;
                if (h_prime < 1.0f) { r = c; g = x_val; b = 0; }
                else if (h_prime < 2.0f) { r = x_val; g = c; b = 0; }
                else if (h_prime < 3.0f) { r = 0; g = c; b = x_val; }
                else if (h_prime < 4.0f) { r = 0; g = x_val; b = c; }
                else if (h_prime < 5.0f) { r = x_val; g = 0; b = c; }
                else { r = c; g = 0; b = x_val; }
                
                int index = (y * width + x) * 3;
                spectrumImage.data[index + 0] = static_cast<unsigned char>((r + m) * 255);
                spectrumImage.data[index + 1] = static_cast<unsigned char>((g + m) * 255);
                spectrumImage.data[index + 2] = static_cast<unsigned char>((b + m) * 255);
            }
        }
        
        bool saved5 = ImageIO::saveImage("test_spectrum.png", spectrumImage);
        std::cout << "   💾 Spectrum salvato: " << (saved5 ? "test_spectrum.png ✓" : "Errore ✗") << std::endl;
        
        std::cout << std::endl;
        
        // Riepilogo
        int totalSaved = (saved1 ? 1 : 0) + (saved2 ? 1 : 0) + (saved3 ? 1 : 0) + 
                        (saved4 ? 1 : 0) + (saved5 ? 1 : 0);
        
        std::cout << "📊 === RIEPILOGO TEST ===" << std::endl;
        std::cout << "🎯 Immagini generate: " << totalSaved << "/5" << std::endl;
        std::cout << "📁 File creati:" << std::endl;
        if (saved1) std::cout << "   • test_gradient.png - Gradient RGB" << std::endl;
        if (saved2) std::cout << "   • test_checkerboard.png - Pattern checkerboard" << std::endl;
        if (saved3) std::cout << "   • test_circle.png - Gradient radiale" << std::endl;
        if (saved4) std::cout << "   • test_noise.png - Pattern noise" << std::endl;
        if (saved5) std::cout << "   • test_spectrum.png - Spettro colori HSV" << std::endl;
        std::cout << std::endl;
        
        if (totalSaved == 5) {
            std::cout << "🎉 SUCCESSO! Tutte le immagini generate correttamente!" << std::endl;
            std::cout << "✨ Sistema Image I/O di PhotonRender completamente funzionante." << std::endl;
        } else {
            std::cout << "⚠️  Alcune immagini non sono state salvate correttamente." << std::endl;
        }
        
        std::cout << std::endl;
        std::cout << "🔧 Capacità dimostrate:" << std::endl;
        std::cout << "   ✅ Generazione immagini programmatica" << std::endl;
        std::cout << "   ✅ Manipolazione pixel diretta" << std::endl;
        std::cout << "   ✅ Pattern generation (gradient, checkerboard, circle)" << std::endl;
        std::cout << "   ✅ Algoritmi matematici (noise, HSV conversion)" << std::endl;
        std::cout << "   ✅ Image I/O (PNG export)" << std::endl;
        std::cout << "   ✅ Color management" << std::endl;
        
        return totalSaved == 5 ? 0 : 1;

    } catch (const std::exception& e) {
        std::cerr << "❌ Errore durante test: " << e.what() << std::endl;
        return 1;
    }
}
