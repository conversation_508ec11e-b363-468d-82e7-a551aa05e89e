// src/core/texture/texture_memory.cpp
// PhotonRender - Advanced Texture Memory Management System Implementation

#include "texture_memory.hpp"
#include <algorithm>
#include <iostream>
#include <cmath>

namespace photon {

TextureMemoryManager& TextureMemoryManager::getInstance() {
    static TextureMemoryManager instance;
    return instance;
}

bool TextureMemoryManager::initialize(const MemorySettings& settings) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    if (m_initialized) {
        return true;
    }
    
    m_settings = settings;
    m_cache.clear();
    m_metrics.reset();
    m_initialized = true;
    
    std::cout << "TextureMemoryManager initialized with " 
              << (m_settings.maxCacheSize / (1024 * 1024)) << "MB cache limit" << std::endl;
    
    return true;
}

void TextureMemoryManager::shutdown() {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    if (!m_initialized) {
        return;
    }
    
    clearCache();
    m_initialized = false;
    
    std::cout << "TextureMemoryManager shutdown complete" << std::endl;
}

bool TextureMemoryManager::cacheTexture(const std::string& key, std::shared_ptr<Texture> texture) {
    if (!texture || key.empty()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    if (!m_initialized) {
        return false;
    }
    
    // Check if texture is too large
    size_t textureSize = texture->getMemoryUsage();
    if (textureSize > m_settings.maxTextureSize) {
        m_metrics.misses++;
        return false;
    }
    
    // Check if already cached
    auto it = m_cache.find(key);
    if (it != m_cache.end()) {
        it->second.updateAccess();
        m_metrics.hits++;
        return true;
    }
    
    // Check if we need to make space
    autoGarbageCollection();
    
    // Check if we still have space after GC
    size_t currentMemory = getTotalCacheMemory();
    if (currentMemory + textureSize > m_settings.maxCacheSize) {
        // Try to evict enough textures
        size_t targetMemory = m_settings.maxCacheSize - textureSize;
        size_t evicted = evictTextures(targetMemory);
        
        if (evicted == 0 && currentMemory + textureSize > m_settings.maxCacheSize) {
            m_metrics.misses++;
            return false;
        }
    }
    
    // Apply optimizations if enabled
    if (m_settings.enableCompression && shouldCompress(texture.get())) {
        // Note: In a real implementation, we would compress the texture here
        // For now, we just mark it as a potential compression candidate
    }
    
    // Cache the texture
    m_cache.emplace(key, TextureCacheEntry(texture));
    m_metrics.allocations++;
    
    return true;
}

std::shared_ptr<Texture> TextureMemoryManager::getCachedTexture(const std::string& key) {
    if (key.empty()) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    if (!m_initialized) {
        return nullptr;
    }
    
    auto it = m_cache.find(key);
    if (it != m_cache.end()) {
        it->second.updateAccess();
        m_metrics.hits++;
        return it->second.texture;
    }
    
    m_metrics.misses++;
    return nullptr;
}

bool TextureMemoryManager::removeCachedTexture(const std::string& key) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    if (!m_initialized) {
        return false;
    }
    
    auto it = m_cache.find(key);
    if (it != m_cache.end()) {
        m_cache.erase(it);
        m_metrics.deallocations++;
        return true;
    }
    
    return false;
}

bool TextureMemoryManager::pinTexture(const std::string& key) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    auto it = m_cache.find(key);
    if (it != m_cache.end()) {
        it->second.isPinned = true;
        return true;
    }
    
    return false;
}

bool TextureMemoryManager::unpinTexture(const std::string& key) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    auto it = m_cache.find(key);
    if (it != m_cache.end()) {
        it->second.isPinned = false;
        return true;
    }
    
    return false;
}

size_t TextureMemoryManager::forceGarbageCollection() {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    if (!m_initialized) {
        return 0;
    }
    
    size_t initialMemory = getTotalCacheMemory();
    size_t targetMemory = static_cast<size_t>(m_settings.maxCacheSize * m_settings.gcTargetRatio);
    
    size_t evicted = evictTextures(targetMemory);
    size_t finalMemory = getTotalCacheMemory();
    
    return initialMemory - finalMemory;
}

void TextureMemoryManager::clearCache() {
    // Note: This method assumes the mutex is already locked
    m_cache.clear();
    m_metrics.deallocations += m_cache.size();
}

MemoryStats TextureMemoryManager::getMemoryStats() const {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    MemoryStats stats;
    stats.totalCached = getTotalCacheMemory();
    stats.totalUsed = stats.totalCached;
    stats.totalAllocated = m_settings.maxCacheSize;
    stats.numTextures = m_cache.size();
    stats.numCachedTextures = m_cache.size();
    
    // Calculate peak usage (simplified)
    stats.peakUsage = std::max(stats.totalUsed, stats.peakUsage);
    
    // Calculate fragmentation ratio (simplified)
    stats.fragmentationRatio = stats.totalAllocated > 0 ? 
        1.0 - (static_cast<double>(stats.totalUsed) / stats.totalAllocated) : 0.0;
    
    return stats;
}

CacheMetrics TextureMemoryManager::getCacheMetrics() const {
    return m_metrics;
}

void TextureMemoryManager::setMemorySettings(const MemorySettings& settings) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_settings = settings;
}

bool TextureMemoryManager::needsGarbageCollection() const {
    if (!m_settings.enableAutoGC) {
        return false;
    }
    
    double usageRatio = getCacheUsageRatio();
    return usageRatio > m_settings.gcThreshold;
}

double TextureMemoryManager::getCacheUsageRatio() const {
    size_t totalMemory = getTotalCacheMemory();
    return m_settings.maxCacheSize > 0 ? 
        static_cast<double>(totalMemory) / m_settings.maxCacheSize : 0.0;
}

size_t TextureMemoryManager::getCachedTextureCount() const {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    return m_cache.size();
}

size_t TextureMemoryManager::getTotalCacheMemory() const {
    // Note: This method assumes the mutex is already locked
    size_t total = 0;
    for (const auto& pair : m_cache) {
        total += pair.second.memoryUsage;
    }
    return total;
}

void TextureMemoryManager::autoGarbageCollection() {
    if (!needsGarbageCollection()) {
        return;
    }
    
    size_t targetMemory = static_cast<size_t>(m_settings.maxCacheSize * m_settings.gcTargetRatio);
    evictTextures(targetMemory);
}

size_t TextureMemoryManager::evictTextures(size_t targetMemory) {
    // Note: This method assumes the mutex is already locked
    
    size_t currentMemory = getTotalCacheMemory();
    if (currentMemory <= targetMemory) {
        return 0;
    }
    
    // Collect eviction candidates (non-pinned textures)
    std::vector<std::pair<std::string, double>> candidates;
    
    for (const auto& pair : m_cache) {
        if (!pair.second.isPinned) {
            double score = 0.0;
            
            switch (m_settings.evictionPolicy) {
                case EvictionPolicy::LRU:
                    score = calculateLRUScore(pair.second);
                    break;
                case EvictionPolicy::LFU:
                    score = calculateLFUScore(pair.second);
                    break;
                case EvictionPolicy::FIFO:
                    score = pair.second.getAge();
                    break;
                case EvictionPolicy::ADAPTIVE:
                    score = calculateAdaptiveScore(pair.second);
                    break;
            }
            
            candidates.emplace_back(pair.first, score);
        }
    }
    
    // Sort by eviction score (higher score = more likely to evict)
    std::sort(candidates.begin(), candidates.end(), 
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    // Evict textures until we reach target memory
    size_t evictedMemory = 0;
    for (const auto& candidate : candidates) {
        if (currentMemory - evictedMemory <= targetMemory) {
            break;
        }
        
        auto it = m_cache.find(candidate.first);
        if (it != m_cache.end()) {
            evictedMemory += it->second.memoryUsage;
            m_cache.erase(it);
            m_metrics.evictions++;
        }
    }
    
    return evictedMemory;
}

double TextureMemoryManager::calculateLRUScore(const TextureCacheEntry& entry) const {
    return entry.getTimeSinceLastAccess();
}

double TextureMemoryManager::calculateLFUScore(const TextureCacheEntry& entry) const {
    return entry.accessCount > 0 ? 1.0 / entry.accessCount : 1.0;
}

double TextureMemoryManager::calculateAdaptiveScore(const TextureCacheEntry& entry) const {
    // Combine multiple factors for adaptive eviction
    double timeFactor = entry.getTimeSinceLastAccess() / 60.0; // Normalize to minutes
    double frequencyFactor = entry.accessCount > 0 ? 1.0 / entry.accessCount : 1.0;
    double sizeFactor = static_cast<double>(entry.memoryUsage) / (1024 * 1024); // Normalize to MB
    
    return (timeFactor * 0.4) + (frequencyFactor * 0.4) + (sizeFactor * 0.2);
}

bool TextureMemoryManager::shouldCompress(const Texture* texture) const {
    if (!texture) {
        return false;
    }
    
    // Check if texture is large enough to benefit from compression
    size_t textureSize = texture->getMemoryUsage();
    size_t compressionThreshold = static_cast<size_t>(m_settings.maxTextureSize * m_settings.compressionThreshold);
    
    return textureSize > compressionThreshold;
}

bool TextureMemoryManager::shouldStream(const Texture* texture) const {
    if (!texture) {
        return false;
    }
    
    // Check if texture is large enough to benefit from streaming
    size_t textureSize = texture->getMemoryUsage();
    return textureSize > m_settings.maxTextureSize / 2;
}

} // namespace photon
