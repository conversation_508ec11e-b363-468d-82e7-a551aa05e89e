// src/test_bump_mapping.cpp
// PhotonRender - Bump Mapping System Test
// Tests bump mapping core functionality and integration

#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include "core/material/material.hpp"
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/vec3.hpp"
#include "core/math/color3.hpp"
#include "core/scene/intersection.hpp"

using namespace photon;

/**
 * @brief Test results structure
 */
struct TestResult {
    std::string name;
    bool passed;
    double duration_ns;
    std::string details;
    
    TestResult(const std::string& n, bool p, double d, const std::string& det = "")
        : name(n), passed(p), duration_ns(d), details(det) {}
};

/**
 * @brief Test suite for bump mapping system
 */
class BumpMappingTestSuite {
private:
    std::vector<TestResult> results;
    
    void addTestResult(const std::string& name, bool passed, double duration_ns = 0.0, const std::string& details = "") {
        results.push_back(TestResult(name, passed, duration_ns, details));
    }
    
    /**
     * @brief Create a simple height map texture for testing
     */
    std::shared_ptr<Texture> createTestHeightMap() {
        // Create a simple procedural height map
        // This would normally be an ImageTexture, but we'll use a simple pattern
        return nullptr; // Placeholder - will implement with actual texture
    }
    
    /**
     * @brief Create test intersection with tangent space
     */
    Intersection createTestIntersection() {
        Intersection isect;
        isect.hit = true;
        isect.p = Vec3(0.0f, 0.0f, 0.0f);
        isect.n = Vec3(0.0f, 0.0f, 1.0f); // Up normal
        isect.uv = Vec2(0.5f, 0.5f);
        isect.hasUV = true;
        
        // Set up tangent space
        isect.dpdu = Vec3(1.0f, 0.0f, 0.0f); // Tangent
        isect.dpdv = Vec3(0.0f, 1.0f, 0.0f); // Bitangent
        isect.hasTangents = true;
        
        return isect;
    }

public:
    /**
     * @brief Test 1: Basic bump mapping functionality
     */
    bool testBasicBumpMapping() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            // Create PBR material
            auto material = std::make_shared<PBRMaterial>();
            
            // Test without bump texture (should return original normal)
            Intersection isect = createTestIntersection();
            Vec3 originalNormal = isect.n;
            Vec3 tangent = isect.getTangent();
            Vec3 bitangent = isect.getBitangent();
            
            Vec3 result = material->applyBumpMapping(isect.getUV(), originalNormal, tangent, bitangent);
            
            // Should return original normal when no bump texture
            bool passed = (result - originalNormal).length() < 1e-6f;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("1.1 Basic Bump Mapping (No Texture)", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("1.1 Basic Bump Mapping (No Texture)", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 2: Bump intensity control
     */
    bool testBumpIntensity() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            // Test intensity clamping
            material->setBumpIntensity(-0.5f); // Should clamp to 0
            bool test1 = (material->getBumpIntensity() == 0.0f);
            
            material->setBumpIntensity(1.5f); // Should clamp to 1
            bool test2 = (material->getBumpIntensity() == 1.0f);
            
            material->setBumpIntensity(0.5f); // Should remain 0.5
            bool test3 = (material->getBumpIntensity() == 0.5f);
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("1.2 Bump Intensity Control", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("1.2 Bump Intensity Control", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 3: Surface mapping integration
     */
    bool testSurfaceMappingIntegration() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            Intersection isect = createTestIntersection();
            
            Vec3 originalNormal = isect.n;
            Vec3 tangent = isect.getTangent();
            Vec3 bitangent = isect.getBitangent();
            
            // Test surface mapping without any textures
            Vec3 result = material->applySurfaceMapping(isect.getUV(), originalNormal, tangent, bitangent);
            
            // Should return original normal when no textures
            bool passed = (result - originalNormal).length() < 1e-6f;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("1.3 Surface Mapping Integration", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("1.3 Surface Mapping Integration", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 4: Performance benchmark
     */
    bool testPerformance() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            Intersection isect = createTestIntersection();
            
            const int iterations = 10000;
            
            // Benchmark bump mapping performance
            auto perfStart = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < iterations; ++i) {
                Vec2 uv(i * 0.0001f, i * 0.0001f);
                material->applyBumpMapping(uv, isect.n, isect.getTangent(), isect.getBitangent());
            }
            
            auto perfEnd = std::chrono::high_resolution_clock::now();
            double totalTime = std::chrono::duration<double, std::nano>(perfEnd - perfStart).count();
            double avgTime = totalTime / iterations;
            
            // Performance target: < 100ns per call
            bool passed = avgTime < 100.0;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Avg: " + std::to_string(avgTime) + "ns per call";
            addTestResult("1.4 Performance Benchmark", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("1.4 Performance Benchmark", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Run all tests
     */
    bool runAllTests() {
        std::cout << "=== PhotonRender Bump Mapping Test Suite ===" << std::endl;
        std::cout << "Testing bump mapping core functionality..." << std::endl << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testBasicBumpMapping();
        allPassed &= testBumpIntensity();
        allPassed &= testSurfaceMappingIntegration();
        allPassed &= testPerformance();
        
        return allPassed;
    }
    
    /**
     * @brief Print test results
     */
    void printResults() {
        std::cout << std::endl << "=== TEST RESULTS ===" << std::endl;
        
        int passed = 0;
        int total = results.size();
        
        for (const auto& result : results) {
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.name;
            
            if (result.duration_ns > 0) {
                std::cout << " (" << std::fixed << std::setprecision(2) 
                          << result.duration_ns << "ns)";
            }
            
            if (!result.details.empty()) {
                std::cout << " - " << result.details;
            }
            
            std::cout << std::endl;
            
            if (result.passed) passed++;
        }
        
        std::cout << std::endl << "Summary: " << passed << "/" << total 
                  << " tests passed (" << (100.0 * passed / total) << "%)" << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Bump mapping system is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    BumpMappingTestSuite testSuite;
    
    bool success = testSuite.runAllTests();
    testSuite.printResults();
    
    return success ? 0 : 1;
}
